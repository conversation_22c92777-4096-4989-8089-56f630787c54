import { z } from 'zod';
import { AgentState } from './types.js';
/**
 * Tool execution context
 */
export interface ToolContext<TState extends AgentState = AgentState> {
    state: TState;
    agentName: string;
    threadId?: string;
    userId?: string;
    metadata?: Record<string, any>;
}
/**
 * Tool execution result
 */
export interface ToolResult {
    success: boolean;
    output: string;
    error?: string;
    metadata?: Record<string, any>;
}
/**
 * Tool approval status
 */
export type ToolApprovalStatus = 'approved' | 'denied' | 'pending';
/**
 * Tool configuration interface
 */
export interface ToolConfig<TParams = any> {
    name: string;
    description: string;
    parameters: z.ZodSchema<TParams> | object;
    needsApproval?: boolean;
    timeout?: number;
    retries?: number;
    rateLimit?: {
        requests: number;
        window: number;
    };
}
/**
 * Tool execution function
 */
export type ToolExecuteFunction<TParams = any, TState extends AgentState = AgentState> = (params: TParams, context: ToolContext<TState>) => Promise<string> | string;
/**
 * Tool approval function
 */
export type ToolApprovalFunction<TParams = any> = (params: TParams, context: ToolContext) => Promise<ToolApprovalStatus> | ToolApprovalStatus;
/**
 * Ultra-high-performance Tool implementation
 *
 * Features:
 * - Zod-based parameter validation
 * - Approval workflows
 * - Rate limiting and timeouts
 * - Streaming execution support
 * - Error handling and retries
 * - Performance monitoring
 */
export declare class Tool<TParams = any, TState extends AgentState = AgentState> {
    private readonly config;
    private readonly executeFunction;
    private readonly approvalFunction?;
    private readonly rateLimitMap;
    constructor(config: ToolConfig<TParams>, executeFunction: ToolExecuteFunction<TParams, TState>, approvalFunction?: ToolApprovalFunction<TParams>);
    /**
     * Execute tool with full validation and error handling
     */
    execute(params: unknown, context: ToolContext<TState>): Promise<ToolResult>;
    /**
     * Validate parameters using Zod schema
     */
    private validateParameters;
    /**
     * Execute with timeout and retry logic
     */
    private executeWithRetries;
    /**
     * Create timeout promise
     */
    private createTimeoutPromise;
    /**
     * Check rate limiting
     */
    private checkRateLimit;
    /**
     * Delay utility for retries
     */
    private delay;
    get name(): string;
    get description(): string;
    get parameters(): z.ZodSchema | object;
    get needsApproval(): boolean;
}
/**
 * Tool registry for dynamic loading and management
 */
export declare class ToolRegistry {
    private readonly tools;
    private readonly categories;
    /**
     * Register a tool
     */
    register(tool: Tool, category?: string): void;
    /**
     * Get tool by name
     */
    get(name: string): Tool | undefined;
    /**
     * Get tools by category
     */
    getByCategory(category: string): Tool[];
    /**
     * List all tools
     */
    list(): Tool[];
    /**
     * Remove tool
     */
    unregister(name: string): boolean;
}
/**
 * Tool map type for performance-optimized lookups
 */
export type ToolMap = Map<string, Tool>;
/**
 * Utility function to create a tool with Zod validation
 */
export declare function tool<TParams = any, TState extends AgentState = AgentState>(config: ToolConfig<TParams>, executeFunction: ToolExecuteFunction<TParams, TState>, approvalFunction?: ToolApprovalFunction<TParams>): Tool<TParams, TState>;
/**
 * Create a tool map from an array of tools
 */
export declare function createToolMap(tools: Tool[]): ToolMap;
/**
 * Global tool registry instance
 */
export declare const globalToolRegistry: ToolRegistry;
//# sourceMappingURL=tools.d.ts.map