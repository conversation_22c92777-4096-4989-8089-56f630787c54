import { describe, test, expect, beforeEach } from '@jest/globals';
import { z } from 'zod';
import {
  Agent,
  Tool,
  Memory,
  createEnhancedAgent,
  assertAgentResult,
  assertPerformance,
  globalPerformanceMeasurer,
  testConfig
} from '../setup.js';

/**
 * Test Suite 2: Planner Agent
 * 
 * Tests the planner agent functionality including:
 * - Task decomposition and planning
 * - Goal setting and tracking
 * - Strategic thinking and reasoning
 * - Plan validation and optimization
 * - Long-horizon planning capabilities
 */

describe('Planner Agent Tests', () => {
  let plannerAgent: Agent;
  let planningTools: Tool[];
  let memory: Memory;

  beforeEach(() => {
    // Create specialized planning tools
    planningTools = [
      new Tool(
        {
          name: 'task_decomposer',
          description: 'Break down complex tasks into smaller subtasks',
          parameters: z.object({
            task: z.string().describe('The complex task to decompose'),
            granularity: z.enum(['high', 'medium', 'low']).default('medium'),
          }),
        },
        async ({ task, granularity }) => {
          const levels = {
            high: 3,
            medium: 5,
            low: 8
          };
          
          const subtasks = Array.from({ length: levels[granularity] }, (_, i) => 
            `Subtask ${i + 1}: ${task.split(' ').slice(0, 2).join(' ')} step ${i + 1}`
          );
          
          return `Task decomposition for "${task}":\n${subtasks.map((st, i) => `${i + 1}. ${st}`).join('\n')}`;
        }
      ),
      new Tool(
        {
          name: 'goal_tracker',
          description: 'Track progress towards goals and milestones',
          parameters: z.object({
            goal: z.string(),
            currentStatus: z.string(),
            milestones: z.array(z.string()).optional(),
          }),
        },
        async ({ goal, currentStatus, milestones = [] }) => {
          const progress = Math.floor(Math.random() * 100);
          return `Goal: ${goal}\nCurrent Status: ${currentStatus}\nProgress: ${progress}%\nMilestones: ${milestones.join(', ')}`;
        }
      ),
      new Tool(
        {
          name: 'resource_estimator',
          description: 'Estimate resources needed for task completion',
          parameters: z.object({
            task: z.string(),
            complexity: z.enum(['simple', 'moderate', 'complex', 'very_complex']),
          }),
        },
        async ({ task, complexity }) => {
          const estimates = {
            simple: { time: '1-2 hours', people: 1, cost: '$100-200' },
            moderate: { time: '1-2 days', people: 2, cost: '$500-1000' },
            complex: { time: '1-2 weeks', people: 3-5, cost: '$2000-5000' },
            very_complex: { time: '1-3 months', people: 5-10, cost: '$10000+' }
          };
          
          const estimate = estimates[complexity];
          return `Resource estimate for "${task}":\nTime: ${estimate.time}\nPeople: ${estimate.people}\nCost: ${estimate.cost}`;
        }
      ),
      new Tool(
        {
          name: 'risk_analyzer',
          description: 'Analyze potential risks and mitigation strategies',
          parameters: z.object({
            plan: z.string(),
            riskLevel: z.enum(['low', 'medium', 'high']).default('medium'),
          }),
        },
        async ({ plan, riskLevel }) => {
          const risks = {
            low: ['Minor delays', 'Small budget overruns'],
            medium: ['Resource conflicts', 'Technical challenges', 'Timeline pressure'],
            high: ['Major scope changes', 'Key personnel unavailable', 'Technology failures']
          };

          const planRisks = risks[riskLevel];
          return `Risk analysis for "${plan}":\n${planRisks.map((risk, i) => `${i + 1}. ${risk}`).join('\n')}\nMitigation: Regular monitoring and contingency planning`;
        }
      ),
      new Tool(
        {
          name: 'timeline_creator',
          description: 'Create project timelines and schedules',
          parameters: z.object({
            tasks: z.array(z.string()),
            duration: z.string().describe('Total project duration'),
          }),
        },
        async ({ tasks, duration }) => {
          const timeline = tasks.map((task, i) => {
            const startWeek = i + 1;
            const endWeek = i + 2;
            return `Week ${startWeek}-${endWeek}: ${task}`;
          });
          
          return `Project Timeline (${duration}):\n${timeline.join('\n')}`;
        }
      )
    ];

    memory = new Memory({
      type: 'summary',
      maxMessages: 30,
    });

    plannerAgent = createEnhancedAgent({
      name: 'Strategic Planner',
      instructions: `You are a strategic planning agent powered by Kimi-K2. Your role is to:
        1. Analyze complex problems and break them down into manageable tasks
        2. Create comprehensive plans with timelines and resource estimates
        3. Identify potential risks and mitigation strategies
        4. Track progress and adjust plans as needed
        5. Think strategically about long-term goals and outcomes

        Available tools:
        - task_decomposer: Break down complex tasks into subtasks
        - goal_tracker: Track progress towards goals and milestones
        - resource_estimator: Estimate resources needed for tasks
        - risk_analyzer: Analyze potential risks and mitigation strategies
        - timeline_creator: Create project timelines and schedules

        Always use the appropriate planning tools to create detailed, actionable plans.`,
      role: 'planner',
      tools: planningTools,
      memory,
      maxIterations: 8, // Planners may need more iterations for complex planning
      enableSelfReflection: true,
    });
  });

  test('should create planner agent with correct configuration', () => {
    expect(plannerAgent.name).toBe('Strategic Planner');
    expect(plannerAgent.role).toBe('planner');
    expect(plannerAgent.tools).toHaveLength(5);
    expect(plannerAgent.tools.map(t => t.name)).toEqual([
      'task_decomposer', 'goal_tracker', 'resource_estimator', 'risk_analyzer', 'timeline_creator'
    ]);
  });

  test('should decompose complex tasks effectively', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await plannerAgent.run(
      'Create a comprehensive plan for launching a new mobile app, including development, testing, marketing, and deployment phases'
    );
    
    const duration = globalPerformanceMeasurer.measure('task_decomposition');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 3);
    
    expect(result.metrics.toolCalls).toBeGreaterThan(0);
    expect(result.finalOutput.toLowerCase()).toContain('subtask');
    expect(result.finalOutput.toLowerCase()).toContain('plan');
  });

  test('should create detailed project timelines', async () => {
    const result = await plannerAgent.run(
      'Plan a 3-month software development project with phases for requirements, design, development, testing, and deployment'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('timeline');
    expect(result.finalOutput.toLowerCase()).toContain('week');
    expect(result.metrics.toolCalls).toBeGreaterThan(0);
  });

  test('should estimate resources accurately', async () => {
    const result = await plannerAgent.run(
      'Estimate the resources needed for building a complex e-commerce platform with user management, payment processing, and inventory management'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('resource');
    expect(result.finalOutput.toLowerCase()).toContain('time');
    expect(result.finalOutput.toLowerCase()).toContain('cost');
  });

  test('should analyze risks and provide mitigation strategies', async () => {
    const result = await plannerAgent.run(
      'Analyze the risks involved in migrating a legacy system to a modern cloud-based architecture and provide mitigation strategies'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('risk');
    expect(result.finalOutput.toLowerCase()).toContain('mitigation');
    expect(result.metrics.toolCalls).toBeGreaterThan(0);
  });

  test('should track goals and milestones', async () => {
    // First, set up a goal
    const setupResult = await plannerAgent.run(
      'Set up tracking for the goal of increasing user engagement by 25% over the next quarter'
    );
    
    assertAgentResult(setupResult);
    
    // Then track progress
    const trackingResult = await plannerAgent.run(
      'Update the progress on our user engagement goal - we have implemented new features and seen a 10% increase so far'
    );
    
    assertAgentResult(trackingResult);
    
    expect(trackingResult.finalOutput.toLowerCase()).toContain('progress');
    expect(trackingResult.finalOutput.toLowerCase()).toContain('goal');
  });

  test('should handle multi-phase planning', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await plannerAgent.run(`
      Create a comprehensive business expansion plan that includes:
      1. Market research and analysis
      2. Product development roadmap
      3. Marketing and sales strategy
      4. Financial projections and funding requirements
      5. Risk assessment and contingency planning
      
      The expansion should target international markets over 18 months.
    `);
    
    const duration = globalPerformanceMeasurer.measure('multi_phase_planning');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 4);
    
    expect(result.metrics.toolCalls).toBeGreaterThan(2);
    expect(result.finalOutput.toLowerCase()).toContain('market');
    expect(result.finalOutput.toLowerCase()).toContain('strategy');
    expect(result.finalOutput.toLowerCase()).toContain('risk');
  });

  test('should demonstrate strategic thinking', async () => {
    const result = await plannerAgent.run(
      'Develop a 5-year strategic plan for a tech startup to become a market leader in AI-powered customer service solutions'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.length).toBeGreaterThan(500); // Should be comprehensive
    expect(result.finalOutput.toLowerCase()).toContain('strategic');
    expect(result.finalOutput.toLowerCase()).toContain('year');
    expect(result.steps.length).toBeGreaterThan(3); // Should involve multiple thinking steps
  });

  test('should adapt plans based on new information', async () => {
    // Initial planning
    const initialResult = await plannerAgent.run(
      'Create a plan for launching a product in 6 months with a budget of $100,000'
    );
    
    assertAgentResult(initialResult);
    
    // Adaptation with new constraints
    const adaptedResult = await plannerAgent.run(
      'The budget has been reduced to $50,000 and timeline shortened to 4 months. Please revise the plan accordingly'
    );
    
    assertAgentResult(adaptedResult);
    
    expect(adaptedResult.finalOutput.toLowerCase()).toContain('revised');
    expect(adaptedResult.finalState.messages.length).toBeGreaterThan(initialResult.finalState.messages.length);
  });

  test('should prioritize tasks effectively', async () => {
    const result = await plannerAgent.run(`
      I have the following tasks to complete:
      - Fix critical bug in production system
      - Prepare quarterly business review presentation
      - Interview candidates for new developer position
      - Update documentation for new features
      - Plan team building event
      
      Help me prioritize these tasks and create an execution plan.
    `);
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('priority');
    expect(result.finalOutput.toLowerCase()).toContain('critical');
    expect(result.metrics.toolCalls).toBeGreaterThan(0);
  });

  test('should handle uncertainty and provide contingency plans', async () => {
    const result = await plannerAgent.run(
      'Plan a product launch where market conditions are uncertain and competitor actions are unpredictable. Include multiple scenarios and contingency plans'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('contingency');
    expect(result.finalOutput.toLowerCase()).toContain('scenario');
    expect(result.finalOutput.toLowerCase()).toContain('uncertain');
  });

  test('should integrate multiple planning tools effectively', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await plannerAgent.run(
      'Plan a complete digital transformation project for a traditional retail company, including technology assessment, implementation roadmap, resource allocation, risk management, and success metrics'
    );
    
    const duration = globalPerformanceMeasurer.measure('integrated_planning');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 5);
    
    // Should use multiple tools
    expect(result.metrics.toolCalls).toBeGreaterThanOrEqual(3);
    
    // Should cover all aspects
    expect(result.finalOutput.toLowerCase()).toContain('transformation');
    expect(result.finalOutput.toLowerCase()).toContain('roadmap');
    expect(result.finalOutput.toLowerCase()).toContain('resource');
    expect(result.finalOutput.toLowerCase()).toContain('risk');
  });

  test('should maintain planning context across iterations', async () => {
    // Phase 1: Initial planning
    const phase1 = await plannerAgent.run(
      'Start planning a new product development cycle. Begin with market analysis and requirements gathering'
    );
    
    assertAgentResult(phase1);
    
    // Phase 2: Continue planning
    const phase2 = await plannerAgent.run(
      'Now continue with the design and development phases of the product we discussed'
    );
    
    assertAgentResult(phase2);
    
    // Should reference previous context
    expect(phase2.finalOutput.toLowerCase()).toContain('product');
    expect(phase2.finalState.messages.length).toBeGreaterThan(phase1.finalState.messages.length);
  });

  test('should provide measurable success criteria', async () => {
    const result = await plannerAgent.run(
      'Create a plan to improve customer satisfaction scores from 3.2 to 4.5 out of 5 within 6 months'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput).toContain('3.2');
    expect(result.finalOutput).toContain('4.5');
    expect(result.finalOutput.toLowerCase()).toContain('satisfaction');
    expect(result.finalOutput.toLowerCase()).toContain('measurable');
  });
});
