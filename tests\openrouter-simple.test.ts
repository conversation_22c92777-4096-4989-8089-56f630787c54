import { describe, test, expect } from '@jest/globals';

/**
 * Simple OpenRouter Test - Direct API Integration
 * 
 * This test directly tests the OpenRouter API integration without
 * relying on the AG3NTIC framework components.
 */

interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface OpenRouterResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

async function callOpenRouter(messages: OpenRouterMessage[]): Promise<string> {
  const apiKey = '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';
  const model = 'moonshotai/kimi-k2';
  
  const requestBody = {
    model,
    messages,
    max_tokens: 2048,
    temperature: 0.7,
    stream: false,
  };

  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://ag3ntic-framework.dev',
      'X-Title': 'AG3NTIC Framework Tests',
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
  }

  const data = await response.json() as OpenRouterResponse;
  
  if (!data.choices || data.choices.length === 0) {
    throw new Error('No completion choices returned from OpenRouter');
  }

  return data.choices[0].message.content;
}

describe('OpenRouter Direct API Tests', () => {
  test('should connect to OpenRouter API', async () => {
    console.log('🔌 Testing OpenRouter API connection...');
    
    const messages: OpenRouterMessage[] = [
      {
        role: 'user',
        content: 'Hello! This is a connection test. Please respond with "Connection successful" if you can read this.'
      }
    ];

    const response = await callOpenRouter(messages);
    
    expect(response).toBeDefined();
    expect(response.length).toBeGreaterThan(10);
    expect(response.toLowerCase()).toContain('connection');
    
    console.log('✅ OpenRouter API Response:', response);
  });

  test('should handle Kimi-K2 model conversation', async () => {
    console.log('🤖 Testing Kimi-K2 conversation...');
    
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: 'You are Kimi-K2, an advanced AI assistant. Please introduce yourself and mention your key capabilities.'
      },
      {
        role: 'user',
        content: 'Hello! Can you tell me about yourself and what makes you unique as an AI assistant?'
      }
    ];

    const response = await callOpenRouter(messages);
    
    expect(response).toBeDefined();
    expect(response.length).toBeGreaterThan(50);
    expect(response.toLowerCase()).toContain('kimi');
    
    console.log('🤖 Kimi-K2 Introduction:', response);
  });

  test('should perform mathematical reasoning', async () => {
    console.log('🧮 Testing mathematical reasoning...');
    
    const messages: OpenRouterMessage[] = [
      {
        role: 'user',
        content: 'Please solve this step by step: What is 15 * 23 + 47 - 12? Show your work.'
      }
    ];

    const response = await callOpenRouter(messages);
    
    expect(response).toBeDefined();
    expect(response.length).toBeGreaterThan(30);
    // 15 * 23 = 345, 345 + 47 = 392, 392 - 12 = 380
    expect(response).toContain('380');
    
    console.log('🧮 Math Solution:', response);
  });

  test('should provide strategic analysis', async () => {
    console.log('📊 Testing strategic analysis...');
    
    const messages: OpenRouterMessage[] = [
      {
        role: 'user',
        content: `Analyze this business scenario: A tech startup has $500K budget, 20 employees, and $2M annual revenue. 
        They want to expand internationally. What are the top 3 considerations and recommendations?`
      }
    ];

    const response = await callOpenRouter(messages);
    
    expect(response).toBeDefined();
    expect(response.length).toBeGreaterThan(100);
    // Check for strategic business terms (flexible matching)
    const hasBusinessTerms = response.toLowerCase().includes('expansion') ||
                            response.toLowerCase().includes('growth') ||
                            response.toLowerCase().includes('strategy') ||
                            response.toLowerCase().includes('market') ||
                            response.toLowerCase().includes('business');
    expect(hasBusinessTerms).toBe(true);
    
    console.log('📊 Strategic Analysis:', response);
  });

  test('should handle creative problem solving', async () => {
    console.log('💡 Testing creative problem solving...');
    
    const messages: OpenRouterMessage[] = [
      {
        role: 'user',
        content: `I need creative ideas for a mobile app that helps people learn new languages. 
        Give me 3 innovative features that would make it stand out from existing apps.`
      }
    ];

    const response = await callOpenRouter(messages);
    
    expect(response).toBeDefined();
    expect(response.length).toBeGreaterThan(100);
    expect(response.toLowerCase()).toContain('language');
    expect(response.toLowerCase()).toContain('app');
    
    console.log('💡 Creative Ideas:', response);
  });

  test('should demonstrate multi-turn conversation', async () => {
    console.log('💬 Testing multi-turn conversation...');
    
    // First turn
    const messages1: OpenRouterMessage[] = [
      {
        role: 'user',
        content: 'I\'m planning a vacation to Japan. What are some must-visit places?'
      }
    ];

    const response1 = await callOpenRouter(messages1);
    expect(response1).toBeDefined();
    expect(response1.toLowerCase()).toContain('japan');
    
    // Second turn - follow up
    const messages2: OpenRouterMessage[] = [
      ...messages1,
      {
        role: 'assistant',
        content: response1
      },
      {
        role: 'user',
        content: 'Great! What\'s the best time of year to visit for cherry blossoms?'
      }
    ];

    const response2 = await callOpenRouter(messages2);
    expect(response2).toBeDefined();
    expect(response2.toLowerCase()).toContain('cherry');
    
    console.log('💬 Japan Travel Advice:', response1.substring(0, 100) + '...');
    console.log('🌸 Cherry Blossom Timing:', response2.substring(0, 100) + '...');
  });

  test('should handle concurrent requests', async () => {
    console.log('⚡ Testing concurrent API requests...');
    
    const topics = ['AI', 'blockchain', 'quantum computing', 'renewable energy', 'space exploration'];
    
    const requests = topics.map(topic => 
      callOpenRouter([{
        role: 'user',
        content: `In one sentence, explain the current state of ${topic} technology.`
      }])
    );

    const responses = await Promise.all(requests);
    
    expect(responses).toHaveLength(5);
    responses.forEach((response, index) => {
      expect(response).toBeDefined();
      expect(response.length).toBeGreaterThan(20);
      console.log(`⚡ ${topics[index]}: ${response.substring(0, 80)}...`);
    });
  });
});
