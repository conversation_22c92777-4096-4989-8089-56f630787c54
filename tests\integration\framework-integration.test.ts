import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { z } from 'zod';
import {
  Agent,
  Graph,
  Tool,
  Memory,
  EvaluationSystem,
  createTestState,
  assertAgentResult,
  assertPerformance,
  assertMemoryUsage,
  testConfig,
  PerformanceMeasurer
} from '../setup.js';

/**
 * Integration Test Suite: Complete Framework Integration
 * 
 * Tests the entire AG3NTIC framework working together including:
 * - End-to-end workflows with all components
 * - Performance under realistic workloads
 * - Memory management and resource optimization
 * - Error handling and recovery
 * - Scalability and reliability
 * - Real-world scenario simulations
 */

describe('Framework Integration Tests', () => {
  let performanceMeasurer: PerformanceMeasurer;
  let memoryBaseline: number;

  beforeEach(() => {
    performanceMeasurer = new PerformanceMeasurer();
    
    // Establish memory baseline
    if (global.gc) {
      global.gc();
    }
    memoryBaseline = process.memoryUsage().heapUsed;
  });

  afterEach(() => {
    // Check for memory leaks
    if (global.gc) {
      global.gc();
    }
    const currentMemory = process.memoryUsage().heapUsed;
    assertMemoryUsage(memoryBaseline, currentMemory, testConfig.performance.maxMemoryIncrease);
  });

  test('should handle complete software development workflow', async () => {
    performanceMeasurer.start();
    
    // Create comprehensive development team
    const productManager = new Agent({
      name: 'Product Manager',
      instructions: 'Define requirements and manage product development lifecycle',
      role: 'planner',
      tools: [
        new Tool(
          {
            name: 'define_requirements',
            description: 'Define product requirements',
            parameters: z.object({
              feature: z.string(),
              priority: z.enum(['low', 'medium', 'high', 'critical']),
            }),
          },
          async ({ feature, priority }) => {
            return `Requirements defined for ${feature} (Priority: ${priority})\n- User stories created\n- Acceptance criteria defined\n- Dependencies identified`;
          }
        )
      ],
      maxIterations: 4,
    });

    const architect = new Agent({
      name: 'System Architect',
      instructions: 'Design system architecture and technical specifications',
      role: 'planner',
      tools: [
        new Tool(
          {
            name: 'design_architecture',
            description: 'Design system architecture',
            parameters: z.object({
              requirements: z.string(),
              scalability: z.enum(['small', 'medium', 'large', 'enterprise']),
            }),
          },
          async ({ requirements, scalability }) => {
            return `Architecture designed for ${scalability} scale based on requirements: "${requirements}":\n- Microservices pattern\n- Database design\n- API specifications\n- Security considerations`;
          }
        )
      ],
      maxIterations: 3,
    });

    const developer = new Agent({
      name: 'Senior Developer',
      instructions: 'Implement features according to specifications',
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'implement_feature',
            description: 'Implement a feature',
            parameters: z.object({
              specification: z.string(),
              technology: z.string(),
            }),
          },
          async ({ specification, technology }) => {
            // Simulate development time
            await new Promise(resolve => setTimeout(resolve, 200));
            return `Feature "${specification}" implemented using ${technology}:\n- Code written and tested\n- Unit tests created\n- Documentation updated`;
          }
        )
      ],
      maxIterations: 3,
    });

    const tester = new Agent({
      name: 'QA Engineer',
      instructions: 'Test features and ensure quality standards',
      role: 'critic',
      tools: [
        new Tool(
          {
            name: 'test_feature',
            description: 'Test implemented feature',
            parameters: z.object({
              feature: z.string(),
              testType: z.enum(['unit', 'integration', 'e2e', 'performance']),
            }),
          },
          async ({ feature, testType }) => {
            const passRate = Math.random() * 0.2 + 0.8; // 80-100% pass rate
            return `${testType} testing completed for ${feature}:\n- Pass rate: ${(passRate * 100).toFixed(1)}%\n- Issues identified and logged\n- Test report generated`;
          }
        )
      ],
      maxIterations: 2,
    });

    // Create development workflow graph
    const devWorkflow = new Graph()
      .addNode('requirements', async (state) => {
        const result = await productManager.run(state);
        return result.finalState;
      })
      .addNode('architecture', async (state) => {
        const result = await architect.run(state);
        return result.finalState;
      })
      .addNode('development', async (state) => {
        const result = await developer.run(state);
        return result.finalState;
      })
      .addNode('testing', async (state) => {
        const result = await tester.run(state);
        return result.finalState;
      })
      .setEntryPoint('requirements')
      .addEdge('requirements', 'architecture')
      .addEdge('architecture', 'development')
      .addEdge('development', 'testing')
      .addEdge('testing', 'END')
      // Graph compiles automatically when executed

    // Execute complete workflow
    const initialState = createTestState({
      messages: [{ 
        role: 'user', 
        content: 'Develop a new user authentication system with OAuth2 integration and multi-factor authentication' 
      }],
      metadata: { project: 'auth-system', sprint: 1 }
    });

    const result = await devWorkflow.execute(initialState);
    
    const duration = performanceMeasurer.measure('complete_dev_workflow');
    
    expect(result).toBeDefined();
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 6);
    
    expect(result.messages.length).toBeGreaterThan(4);
    expect(result.metadata?.project).toBe('auth-system');
  });

  test('should handle high-volume concurrent operations', async () => {
    performanceMeasurer.start();
    
    // Create multiple agents for load testing
    const agents = Array.from({ length: 10 }, (_, i) => 
      new Agent({
        name: `Load Test Agent ${i + 1}`,
        instructions: 'Handle concurrent requests efficiently',
        role: 'executor',
        tools: [
          new Tool(
            {
              name: 'process_request',
              description: 'Process a request',
              parameters: z.object({ requestId: z.string() }),
            },
            async ({ requestId }) => {
              // Simulate processing time
              await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
              return `Request ${requestId} processed successfully`;
            }
          )
        ],
        maxIterations: 2,
      })
    );

    // Execute 50 concurrent requests
    const concurrentRequests = Array.from({ length: 50 }, (_, i) => 
      agents[i % agents.length].run(`Process request REQ-${i + 1}`)
    );

    const results = await Promise.all(concurrentRequests);
    
    const duration = performanceMeasurer.measure('high_volume_concurrent');
    
    results.forEach(result => assertAgentResult(result));
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 10);
    
    // All requests should complete successfully
    expect(results).toHaveLength(50);
    expect(results.every(r => r.finalOutput.includes('processed successfully'))).toBe(true);
  });

  test('should demonstrate memory efficiency under sustained load', async () => {
    performanceMeasurer.start();
    
    const memoryEfficientAgent = new Agent({
      name: 'Memory Efficient Agent',
      instructions: 'Process tasks with minimal memory footprint',
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'memory_efficient_task',
            description: 'Process task efficiently',
            parameters: z.object({ data: z.array(z.number()) }),
          },
          async ({ data }) => {
            // Process data without creating large intermediate objects
            const sum = data.reduce((acc, val) => acc + val, 0);
            const avg = sum / data.length;
            return `Processed ${data.length} items: sum=${sum}, avg=${avg.toFixed(2)}`;
          }
        )
      ],
      memory: new Memory({ type: 'window', windowSize: 5 }), // Limited memory
      maxIterations: 2,
    });

    // Process 100 tasks with large datasets
    const memoryTasks = Array.from({ length: 100 }, async (_, i) => {
      const largeDataset = Array.from({ length: 1000 }, () => Math.random() * 100);
      return memoryEfficientAgent.run(`Process dataset ${i + 1}: ${JSON.stringify(largeDataset.slice(0, 10))}...`);
    });

    // Execute in batches to control memory usage
    const batchSize = 10;
    const results = [];
    
    for (let i = 0; i < memoryTasks.length; i += batchSize) {
      const batch = memoryTasks.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch);
      results.push(...batchResults);
      
      // Force garbage collection between batches
      if (global.gc) {
        global.gc();
      }
    }
    
    const duration = performanceMeasurer.measure('memory_efficiency_test');
    
    results.forEach(result => assertAgentResult(result));
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 15);
    
    expect(results).toHaveLength(100);
  });

  test('should handle complex error scenarios and recovery', async () => {
    performanceMeasurer.start();
    
    // Create agents with different failure modes
    const unreliableAgent = new Agent({
      name: 'Unreliable Agent',
      instructions: 'Sometimes fail to test error handling',
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'unreliable_operation',
            description: 'Operation that sometimes fails',
            parameters: z.object({ operation: z.string() }),
            retries: 3,
          },
          async ({ operation }) => {
            if (Math.random() < 0.3) { // 30% failure rate
              throw new Error(`Operation ${operation} failed temporarily`);
            }
            return `Operation ${operation} completed successfully`;
          }
        )
      ],
      maxIterations: 2,
    });

    const recoveryAgent = new Agent({
      name: 'Recovery Agent',
      instructions: 'Handle recovery from failures',
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'recover_from_failure',
            description: 'Recover from system failures',
            parameters: z.object({ failureType: z.string() }),
          },
          async ({ failureType }) => {
            return `Recovery initiated for ${failureType}: System restored to stable state`;
          }
        )
      ],
      maxIterations: 2,
    });

    // Create error handling graph
    const errorHandlingGraph = new Graph()
      .addNode('primary', async (state) => {
        try {
          const result = await unreliableAgent.run(state);
          return result.finalState;
        } catch (error) {
          return {
            ...state,
            metadata: { ...state.metadata, error: error instanceof Error ? error.message : String(error), needsRecovery: true }
          };
        }
      })
      .addNode('recovery', async (state) => {
        const result = await recoveryAgent.run(state);
        return result.finalState;
      })
      .setEntryPoint('primary')
      .addConditionalEdge('primary', (state) => {
        return state.metadata?.needsRecovery ? 'recovery' : 'END';
      }, {
        recovery: 'END',
        END: 'END'
      })
      .addRetry('primary', {
        maxAttempts: 3,
        backoffMs: 500,
        exponential: true,
      })
      .compile();

    // Test error handling with multiple scenarios
    const errorScenarios = Array.from({ length: 20 }, (_, i) => 
      errorHandlingGraph.run(createTestState({
        messages: [{ role: 'user', content: `Execute operation ${i + 1}` }],
        metadata: { operationId: i + 1 }
      }))
    );

    const results = await Promise.all(errorScenarios);
    
    const duration = performanceMeasurer.measure('error_handling_test');
    
    results.forEach(result => expect(result).toBeDefined());
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 8);
    
    // Some operations should succeed, some should trigger recovery
    const successfulOps = results.filter(r => 
      r.messages.some(m => m.content.includes('completed successfully'))
    );
    const recoveredOps = results.filter(r => 
      r.messages.some(m => m.content.includes('Recovery initiated'))
    );
    
    expect(successfulOps.length + recoveredOps.length).toBe(20);
  });

  test('should integrate evaluation system with multi-agent workflows', async () => {
    performanceMeasurer.start();
    
    const evaluationSystem = new EvaluationSystem({
      enableSelfEvaluation: true,
      enablePeerReview: true,
      enableHumanReview: false,
      metrics: ['accuracy', 'efficiency', 'collaboration'],
    });

    const evaluatedAgent = new Agent({
      name: 'Evaluated Agent',
      instructions: 'Perform tasks while being evaluated for quality',
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'evaluated_task',
            description: 'Perform a task that will be evaluated',
            parameters: z.object({ task: z.string(), complexity: z.enum(['simple', 'complex']) }),
          },
          async ({ task, complexity }) => {
            const quality = complexity === 'simple' ? 0.9 : 0.7;
            return `Task "${task}" completed with ${quality} quality score`;
          }
        )
      ],
      maxIterations: 3,
    });

    const reviewerAgent = new Agent({
      name: 'Reviewer Agent',
      instructions: 'Review and evaluate other agents work',
      role: 'critic',
      tools: [],
      maxIterations: 2,
    });

    // Configure evaluation system with reviewer
    evaluationSystem.setEvaluationAgent(reviewerAgent);

    // Execute tasks with evaluation
    const evaluatedTasks = [
      'Implement user authentication',
      'Design database schema',
      'Create API endpoints',
      'Write unit tests',
      'Deploy to production'
    ];

    const evaluationResults = [];
    
    for (const task of evaluatedTasks) {
      const result = await evaluatedAgent.run(`Execute task: ${task}`);
      
      const evaluation = await evaluationSystem.evaluate(
        evaluatedAgent.name,
        task,
        result.finalOutput,
        { complexity: task.includes('Deploy') ? 'complex' : 'simple' }
      );
      
      evaluationResults.push({ task, result, evaluation });
    }
    
    const duration = performanceMeasurer.measure('evaluation_integration');
    
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 8);
    
    // Check evaluation results
    expect(evaluationResults).toHaveLength(5);
    evaluationResults.forEach(({ evaluation }) => {
      expect(evaluation.score).toBeGreaterThan(0);
      expect(evaluation.metrics).toBeDefined();
      expect(evaluation.feedback).toBeDefined();
    });

    // Get scorecard
    const scorecard = evaluationSystem.getScorecard(evaluatedAgent.name);
    expect(scorecard).toBeDefined();
    expect(scorecard!.totalEvaluations).toBe(5);
    expect(scorecard!.averageScore).toBeGreaterThan(0);
  });

  test('should demonstrate real-world customer service scenario', async () => {
    performanceMeasurer.start();
    
    // Create customer service team
    const frontlineAgent = new Agent({
      name: 'Frontline Support',
      instructions: 'Handle initial customer inquiries and route complex issues',
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'handle_inquiry',
            description: 'Handle customer inquiry',
            parameters: z.object({
              inquiry: z.string(),
              priority: z.enum(['low', 'medium', 'high', 'urgent']),
            }),
          },
          async ({ inquiry, priority }) => {
            const responseTime = { low: 100, medium: 200, high: 300, urgent: 50 }[priority];
            await new Promise(resolve => setTimeout(resolve, responseTime));
            return `Inquiry handled: "${inquiry}" (Priority: ${priority}) - Initial response provided`;
          }
        )
      ],
      maxIterations: 3,
    });

    const technicalAgent = new Agent({
      name: 'Technical Support',
      instructions: 'Handle technical issues and provide detailed solutions',
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'diagnose_technical_issue',
            description: 'Diagnose and resolve technical issues',
            parameters: z.object({ issue: z.string(), severity: z.enum(['low', 'medium', 'high']) }),
          },
          async ({ issue, severity }) => {
            const diagnosticTime = { low: 200, medium: 500, high: 800 }[severity];
            await new Promise(resolve => setTimeout(resolve, diagnosticTime));
            return `Technical diagnosis for "${issue}": Root cause identified, solution provided`;
          }
        )
      ],
      maxIterations: 4,
    });

    const escalationAgent = new Agent({
      name: 'Escalation Manager',
      instructions: 'Handle escalated issues and coordinate resolution',
      role: 'orchestrator',
      tools: [
        new Tool(
          {
            name: 'manage_escalation',
            description: 'Manage escalated customer issues',
            parameters: z.object({ issue: z.string(), customerTier: z.enum(['basic', 'premium', 'enterprise']) }),
          },
          async ({ issue, customerTier }) => {
            const priorityLevel = { basic: 'standard', premium: 'high', enterprise: 'critical' }[customerTier];
            return `Escalation managed for "${issue}" - Priority: ${priorityLevel}, Senior team assigned`;
          }
        )
      ],
      handoffs: [frontlineAgent, technicalAgent],
      maxIterations: 5,
    });

    // Simulate customer service scenarios
    const customerScenarios = [
      { inquiry: 'Password reset not working', type: 'simple', priority: 'medium' },
      { inquiry: 'Application crashes on startup', type: 'technical', priority: 'high' },
      { inquiry: 'Billing discrepancy for enterprise account', type: 'escalation', priority: 'urgent' },
      { inquiry: 'Feature request for mobile app', type: 'simple', priority: 'low' },
      { inquiry: 'Data synchronization issues', type: 'technical', priority: 'high' },
    ];

    const serviceResults = await Promise.all(
      customerScenarios.map(async (scenario) => {
        let agent = frontlineAgent;
        
        if (scenario.type === 'technical') {
          agent = technicalAgent;
        } else if (scenario.type === 'escalation') {
          agent = escalationAgent;
        }
        
        return agent.run(`Handle customer inquiry: ${scenario.inquiry} (Priority: ${scenario.priority})`);
      })
    );
    
    const duration = performanceMeasurer.measure('customer_service_scenario');
    
    serviceResults.forEach(result => assertAgentResult(result));
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 6);
    
    expect(serviceResults).toHaveLength(5);
    expect(serviceResults.every(r => r.finalOutput.includes('handled') || r.finalOutput.includes('managed'))).toBe(true);
  });

  test('should maintain framework stability over extended operation', async () => {
    performanceMeasurer.start();
    
    const stableAgent = new Agent({
      name: 'Stability Test Agent',
      instructions: 'Maintain consistent performance over time',
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'stability_task',
            description: 'Perform consistent task',
            parameters: z.object({ iteration: z.number() }),
          },
          async ({ iteration }) => {
            // Simulate varying workload
            const workload = 50 + (iteration % 10) * 10;
            await new Promise(resolve => setTimeout(resolve, workload));
            return `Stability task ${iteration} completed in ${workload}ms`;
          }
        )
      ],
      memory: new Memory({ type: 'buffer', maxMessages: 10 }),
      maxIterations: 2,
    });

    // Run 100 iterations to test stability
    const stabilityResults = [];
    const durations = [];
    
    for (let i = 1; i <= 100; i++) {
      const iterationStart = performance.now();
      
      try {
        const result = await stableAgent.run(`Execute stability test iteration ${i}`);
        const iterationDuration = performance.now() - iterationStart;
        
        stabilityResults.push(result);
        durations.push(iterationDuration);
        
        // Periodic garbage collection
        if (i % 20 === 0 && global.gc) {
          global.gc();
        }
        
      } catch (error) {
        console.warn(`Stability test iteration ${i} failed:`, error);
      }
    }
    
    const totalDuration = performanceMeasurer.measure('stability_test');
    
    // Analyze stability metrics
    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const maxDuration = Math.max(...durations);
    const minDuration = Math.min(...durations);
    const variance = durations.reduce((sum, d) => sum + Math.pow(d - avgDuration, 2), 0) / durations.length;
    const stdDev = Math.sqrt(variance);
    
    // Stability assertions
    expect(stabilityResults.length).toBeGreaterThan(95); // >95% success rate
    expect(stdDev).toBeLessThan(avgDuration * 0.5); // Consistent performance
    expect(maxDuration).toBeLessThan(avgDuration * 3); // No extreme outliers
    assertPerformance(totalDuration, testConfig.performance.maxExecutionTime * 50);
    
    console.log(`Stability Test Results:
      - Success Rate: ${(stabilityResults.length / 100 * 100).toFixed(1)}%
      - Avg Duration: ${avgDuration.toFixed(2)}ms
      - Std Deviation: ${stdDev.toFixed(2)}ms
      - Min/Max: ${minDuration.toFixed(2)}ms / ${maxDuration.toFixed(2)}ms`);
  });
});
