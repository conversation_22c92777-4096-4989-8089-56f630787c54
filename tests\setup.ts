import { beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { Agent, Graph, Memory, Tool, EvaluationSystem } from '../src/index.js';
import { AgentState } from '../src/core/types.js';
import { AgentRole } from '../src/core/agent.js';
import {
  OpenRouterClient,
  createOpenRouterClient,
  openRouterTestConfig
} from './providers/openrouter.js';

/**
 * Test utilities and setup for AG3NTIC framework tests
 */

// OpenRouter client for real LLM interactions
export let openRouterClient: OpenRouterClient;

// Test state factory
export function createTestState(overrides: Partial<AgentState> = {}): AgentState {
  return {
    messages: [
      { role: 'user', content: 'Test message' }
    ],
    metadata: {
      agentName: 'test-agent',
      role: 'executor',
      threadId: 'test-thread-123',
      ...overrides.metadata
    },
    ...overrides
  };
}

// Real tool factory for testing
export function createTestTool(name: string, description: string, handler: (params: any) => Promise<string>): Tool {
  return new Tool(
    {
      name,
      description,
      parameters: {
        type: 'object',
        properties: {
          input: { type: 'string' }
        },
        required: ['input']
      }
    },
    handler
  );
}

// Real memory factory for testing
export function createTestMemory(): Memory {
  return new Memory({
    type: 'buffer',
    maxMessages: 10
  });
}

// Real evaluation system factory for testing
export function createTestEvaluationSystem(): EvaluationSystem {
  return new EvaluationSystem({
    enableSelfEvaluation: true,
    enablePeerReview: false,
    enableHumanReview: false,
    metrics: ['accuracy', 'relevance', 'completeness']
  });
}

// Test data generators
export const testData = {
  simpleQuery: "What is the weather today?",
  complexQuery: "Analyze the sales data from last quarter and create a comprehensive report with recommendations",
  planningQuery: "Create a project plan for building a new mobile app",
  analysisQuery: "Analyze this dataset and provide insights: [1,2,3,4,5,6,7,8,9,10]",
  orchestrationQuery: "I need help with both data analysis and creating a presentation"
};

// Performance measurement utilities
export class PerformanceMeasurer {
  private startTime: number = 0;
  private measurements: Array<{ name: string; duration: number }> = [];

  start(): void {
    this.startTime = performance.now();
  }

  measure(name: string): number {
    const duration = performance.now() - this.startTime;
    this.measurements.push({ name, duration });
    return duration;
  }

  getResults(): Array<{ name: string; duration: number }> {
    return [...this.measurements];
  }

  reset(): void {
    this.measurements = [];
    this.startTime = 0;
  }
}

// Test assertion helpers
export function assertAgentResult(result: any, expectedProperties: string[] = []) {
  expect(result).toBeDefined();
  expect(result.finalOutput).toBeDefined();
  expect(result.finalState).toBeDefined();
  expect(result.steps).toBeDefined();
  expect(result.metrics).toBeDefined();
  
  expectedProperties.forEach(prop => {
    expect(result).toHaveProperty(prop);
  });
}

export function assertPerformance(duration: number, maxExpectedMs: number) {
  expect(duration).toBeLessThan(maxExpectedMs);
}

export function assertMemoryUsage(beforeHeap: number, afterHeap: number, maxIncreaseMB: number) {
  const increaseMB = (afterHeap - beforeHeap) / 1024 / 1024;
  expect(increaseMB).toBeLessThan(maxIncreaseMB);
}

// Global test setup
let globalPerformanceMeasurer: PerformanceMeasurer;

beforeAll(async () => {
  globalPerformanceMeasurer = new PerformanceMeasurer();

  // Initialize OpenRouter client - FAIL if connection fails
  console.log('🔌 Connecting to OpenRouter with Kimi-K2 model...');

  openRouterClient = createOpenRouterClient(openRouterTestConfig.apiKey);

  // Test connection with a simple request - MUST succeed
  try {
    const testResponse = await openRouterClient.generateCompletion([
      { role: 'user', content: 'Hello, this is a connection test.' }
    ]);

    if (!testResponse || testResponse.length === 0) {
      throw new Error('Empty response from OpenRouter API');
    }

    console.log('✅ OpenRouter connection established - using Kimi-K2 model');
    console.log('🚀 Starting AG3NTIC Framework Test Suite');
    console.log('🤖 LLM Provider: OpenRouter (Kimi-K2) - REAL API ONLY');

  } catch (error) {
    console.error('❌ OpenRouter connection FAILED - tests will fail');
    console.error('Error:', error instanceof Error ? error.message : String(error));
    throw new Error(`OpenRouter API connection required for tests. Error: ${error instanceof Error ? error.message : String(error)}`);
  }
});

afterAll(async () => {
  console.log('✅ AG3NTIC Framework Test Suite Completed');
  const results = globalPerformanceMeasurer.getResults();
  if (results.length > 0) {
    console.log('📊 Performance Summary:');
    results.forEach(({ name, duration }) => {
      console.log(`  ${name}: ${duration.toFixed(2)}ms`);
    });
  }
});

beforeEach(() => {
  globalPerformanceMeasurer.start();
});

afterEach(() => {
  // Clean up any test artifacts
  globalPerformanceMeasurer.reset();
});

export { globalPerformanceMeasurer };

// Enhanced agent factory with OpenRouter integration - REAL API ONLY
export function createEnhancedAgent(config: {
  name: string;
  instructions: string;
  role?: AgentRole;
  tools?: Tool[];
  memory?: Memory;
  maxIterations?: number;
  enableSelfReflection?: boolean;
}): Agent {
  // Create base agent
  const agent = new Agent({
    maxIterations: 5,
    ...config,
  });

  // Override the run method to use OpenRouter - NO FALLBACKS
  agent.run = async function(input: string | any, context?: Record<string, any>) {
    const state = typeof input === 'string'
      ? { messages: [{ role: 'user', content: input }], metadata: { agentName: config.name } }
      : input;

    // Check if tools should be used
    const toolNames = config.tools?.map(t => t.name) || [];
    if (toolNames.length > 0) {
      const toolDecision = await openRouterClient.shouldUseTool(
        state,
        toolNames,
        config.instructions
      );

      if (toolDecision.shouldUse && toolDecision.toolName && config.tools) {
        const tool = config.tools.find(t => t.name === toolDecision.toolName);
        if (tool) {
          const toolResult = await tool.execute(
            toolDecision.parameters || {},
            { state, agentName: config.name }
          );

          if (toolResult.success) {
            // Generate follow-up response after tool usage
            const followUpState = {
              ...state,
              messages: [
                ...state.messages,
                { role: 'assistant', content: `I used the ${toolDecision.toolName} tool and got: ${toolResult.output}` }
              ]
            };

            const finalResponse = await openRouterClient.generateAgentResponse(
              followUpState,
              `${config.instructions}\n\nPlease provide a comprehensive response based on the tool result.`
            );

            return {
              finalOutput: finalResponse,
              finalState: {
                ...followUpState,
                messages: [...followUpState.messages, { role: 'assistant', content: finalResponse }]
              },
              finalAgent: agent,
              steps: [
                { step: 'thought', content: 'Analyzing task and selecting appropriate tool', timestamp: Date.now() },
                { step: 'action', content: `Using tool: ${toolDecision.toolName}`, timestamp: Date.now() },
                { step: 'observation', content: toolResult.output, timestamp: Date.now() },
                { step: 'thought', content: 'Generating comprehensive response based on tool result', timestamp: Date.now() }
              ],
              metrics: {
                totalSteps: 4,
                toolCalls: 1,
                executionTime: Date.now() - Date.now(),
              }
            };
          }
        }
      }
    }

    // Generate response using OpenRouter
    const response = await openRouterClient.generateAgentResponse(
      state,
      config.instructions,
      context ? JSON.stringify(context) : undefined
    );

    return {
      finalOutput: response,
      finalState: {
        ...state,
        messages: [...state.messages, { role: 'assistant', content: response }]
      },
      finalAgent: agent,
      steps: [
        { step: 'thought', content: 'Processing request and generating intelligent response', timestamp: Date.now() },
        { step: 'observation', content: response, timestamp: Date.now() }
      ],
      metrics: {
        totalSteps: 2,
        toolCalls: 0,
        executionTime: Date.now() - Date.now(),
      }
    };
  };

  return agent;
}

// Real LLM provider - NO MOCKS
export class RealLLMProvider {
  async generateResponse(prompt: string, context?: any): Promise<string> {
    // Context can be used for additional prompt engineering if needed
    const messages = context?.messages || [{ role: 'user', content: prompt }];
    return openRouterClient.generateCompletion(messages);
  }
}

// Test configuration
export const testConfig = {
  timeout: 30000, // 30 seconds max per test
  performance: {
    maxExecutionTime: 1000, // 1 second max for most operations
    maxMemoryIncrease: 50, // 50MB max memory increase
  },
  agents: {
    maxIterations: 5,
    defaultTimeout: 5000,
  }
};

// Export commonly used test utilities
export {
  Agent,
  Graph,
  Memory,
  Tool,
  EvaluationSystem
};
