"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Agent = void 0;
exports.run = run;
const zod_1 = require("zod");
const tools_js_1 = require("./tools.js");
const memory_js_1 = require("./memory.js");
class OpenRouterClient {
    constructor(apiKey, model = 'moonshotai/kimi-k2') {
        this.apiKey = apiKey;
        this.model = model;
    }
    async generateCompletion(messages) {
        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://ag3ntic-framework.dev',
                'X-Title': 'AG3NTIC Framework',
            },
            body: JSON.stringify({
                model: this.model,
                messages,
                max_tokens: 2048,
                temperature: 0.7,
                stream: false,
            }),
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
        }
        const data = await response.json();
        return data.choices[0].message.content;
    }
    convertMessages(messages) {
        return messages.map(msg => ({
            role: msg.role,
            content: msg.content || '',
        }));
    }
}
/**
 * Ultra-high-performance Agent implementation
 *
 * Features:
 * - ReAct (Thought > Action > Observation) loop
 * - Agent handoffs and orchestration
 * - Self-reflection and critique
 * - Role-based specialization
 * - Memory integration
 * - Performance optimizations
 */
class Agent {
    constructor(config) {
        this.config = {
            role: 'executor',
            tools: [],
            handoffs: [],
            memory: new memory_js_1.Memory(),
            modelSettings: {},
            maxIterations: 10,
            enableSelfReflection: false,
            ...config,
        };
        // Initialize OpenRouter client
        this.openRouterClient = new OpenRouterClient('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', 'moonshotai/kimi-k2');
        // Pre-compile tool map for O(1) lookup
        this.toolMap = new Map();
        this.config.tools.forEach(tool => {
            this.toolMap.set(tool.name, tool);
        });
        // Pre-compile handoff map
        this.handoffMap = new Map();
        this.config.handoffs.forEach(agent => {
            this.handoffMap.set(agent.name, agent);
        });
    }
    /**
     * Create agent with type safety for handoffs
     */
    static create(config) {
        return new Agent(config);
    }
    /**
     * Convert agent to tool for use by other agents
     */
    asTool() {
        return new tools_js_1.Tool({
            name: `agent_${this.config.name.toLowerCase().replace(/\s+/g, '_')}`,
            description: `Delegate to ${this.config.name}: ${this.config.instructions}`,
            parameters: zod_1.z.object({
                input: zod_1.z.string().describe('Input to send to the agent'),
            }),
        }, async ({ input }) => {
            const result = await this.run(input);
            return result.finalOutput;
        });
    }
    /**
     * Execute ReAct loop with performance optimizations
     */
    async run(input, context) {
        const startTime = Date.now();
        const steps = [];
        let currentState;
        let iteration = 0;
        let toolCalls = 0;
        // Initialize state
        if (typeof input === 'string') {
            currentState = {
                messages: [{ role: 'user', content: input }],
                metadata: { agentName: this.config.name, role: this.config.role },
                ...context,
            };
        }
        else {
            currentState = { ...input };
        }
        // Load memory if available
        if (this.config.memory) {
            currentState = await this.config.memory.load(currentState);
        }
        // ReAct loop with optimized execution
        while (iteration < this.config.maxIterations) {
            iteration++;
            try {
                // THOUGHT: Generate reasoning
                const thoughtStep = await this.generateThought(currentState);
                steps.push({
                    step: 'thought',
                    content: thoughtStep.content,
                    timestamp: Date.now(),
                });
                // Check for handoff decision
                const handoffAgent = this.checkForHandoff(thoughtStep.content);
                if (handoffAgent) {
                    const handoffResult = await handoffAgent.run(currentState, context);
                    return {
                        ...handoffResult,
                        finalAgent: handoffAgent,
                        steps: [...steps, ...handoffResult.steps],
                    };
                }
                // ACTION: Execute tool if needed
                const actionStep = await this.executeAction(currentState, thoughtStep);
                if (actionStep) {
                    steps.push({
                        step: 'action',
                        content: actionStep.content,
                        timestamp: Date.now(),
                    });
                    toolCalls++;
                    // OBSERVATION: Process tool result
                    const observationStep = await this.processObservation(currentState, actionStep);
                    steps.push({
                        step: 'observation',
                        content: observationStep.content,
                        timestamp: Date.now(),
                    });
                    currentState = observationStep.state;
                }
                // Check for completion
                if (this.isComplete(currentState)) {
                    break;
                }
                // REFLECTION: Self-critique if enabled
                if (this.config.enableSelfReflection && iteration % 3 === 0) {
                    const reflectionStep = await this.generateReflection(currentState);
                    steps.push({
                        step: 'reflection',
                        content: reflectionStep.content,
                        timestamp: Date.now(),
                    });
                }
            }
            catch (error) {
                // Handle errors gracefully
                steps.push({
                    step: 'observation',
                    content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    timestamp: Date.now(),
                });
                break;
            }
        }
        // Save memory if available
        if (this.config.memory) {
            await this.config.memory.save(currentState);
        }
        const executionTime = Date.now() - startTime;
        const finalOutput = this.extractFinalOutput(currentState);
        return {
            finalOutput,
            finalState: currentState,
            finalAgent: this,
            steps,
            metrics: {
                totalSteps: steps.length,
                toolCalls,
                executionTime,
            },
        };
    }
    /**
     * Generate thought step with role-specific reasoning using OpenRouter
     */
    async generateThought(state) {
        try {
            // Create system message with role-specific instructions
            const systemMessage = {
                role: 'system',
                content: `You are ${this.config.name}, a ${this.config.role} agent. ${this.config.instructions}

Available tools: ${this.config.tools.map(t => `${t.name}: ${t.description}`).join(', ')}

Your task is to analyze the current situation and determine the next step.
Respond with your reasoning and whether you need to use tools.
Format: THOUGHT: [your reasoning] | TOOLS: [yes/no]`
            };
            // Convert state messages to OpenRouter format
            const messages = [
                systemMessage,
                ...this.openRouterClient.convertMessages(state.messages)
            ];
            const response = await this.openRouterClient.generateCompletion(messages);
            // Parse response for tool usage decision
            const shouldUseTools = response.toLowerCase().includes('tools: yes') ||
                response.toLowerCase().includes('use tool') ||
                this.config.tools.some(tool => response.toLowerCase().includes(tool.name));
            return {
                content: response,
                shouldUseTools,
            };
        }
        catch (error) {
            console.warn('OpenRouter thought generation failed, using fallback:', error);
            return {
                content: `Analyzing the current situation and determining next steps... (Error: ${error instanceof Error ? error.message : 'Unknown'})`,
                shouldUseTools: false,
            };
        }
    }
    /**
     * Check if thought indicates handoff to another agent
     */
    checkForHandoff(thought) {
        // Simple keyword-based handoff detection
        // In production, this would use more sophisticated NLP
        for (const [name, agent] of this.handoffMap) {
            if (thought.toLowerCase().includes(name.toLowerCase())) {
                return agent;
            }
        }
        return null;
    }
    /**
     * Execute action step (tool usage) with OpenRouter intelligence
     */
    async executeAction(state, thought) {
        if (!thought.shouldUseTools || this.config.tools.length === 0)
            return null;
        try {
            // Use OpenRouter to select tool and parameters
            const toolSelectionPrompt = [
                {
                    role: 'system',
                    content: `You are ${this.config.name}. Select the best tool and parameters for the current task.

Available tools:
${this.config.tools.map(t => `${t.name}: ${t.description}\nParameters: ${JSON.stringify(t.schema.shape || {})}`).join('\n\n')}

Respond with JSON format: {"toolName": "tool_name", "parameters": {...}}
If no tool is needed, respond with: {"toolName": null}`
                },
                ...this.openRouterClient.convertMessages(state.messages),
                {
                    role: 'user',
                    content: `Based on this reasoning: "${thought.content}", which tool should I use and with what parameters?`
                }
            ];
            const toolResponse = await this.openRouterClient.generateCompletion(toolSelectionPrompt);
            // Parse JSON response
            const jsonMatch = toolResponse.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                return null;
            }
            const toolDecision = JSON.parse(jsonMatch[0]);
            if (!toolDecision.toolName) {
                return null;
            }
            // Execute the selected tool
            const tool = this.toolMap.get(toolDecision.toolName);
            if (!tool) {
                return {
                    content: `Tool ${toolDecision.toolName} not found`,
                    toolName: toolDecision.toolName,
                    result: { success: false, error: 'Tool not found' }
                };
            }
            const toolResult = await tool.execute(toolDecision.parameters || {}, {
                agentName: this.config.name,
                state
            });
            return {
                content: `Used ${toolDecision.toolName}: ${toolResult.output}`,
                toolName: toolDecision.toolName,
                result: toolResult
            };
        }
        catch (error) {
            console.warn('Tool execution failed:', error);
            return {
                content: `Tool execution error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                toolName: 'unknown',
                result: { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
            };
        }
    }
    /**
     * Process observation from tool execution with OpenRouter intelligence
     */
    async processObservation(state, action) {
        try {
            // Use OpenRouter to generate intelligent response based on tool result
            const observationPrompt = [
                {
                    role: 'system',
                    content: `You are ${this.config.name}. Process the tool result and provide a comprehensive response.

Tool used: ${action.toolName}
Tool result: ${JSON.stringify(action.result)}

Provide a natural, helpful response based on the tool execution result.`
                },
                ...this.openRouterClient.convertMessages(state.messages),
                {
                    role: 'assistant',
                    content: action.content
                }
            ];
            const response = await this.openRouterClient.generateCompletion(observationPrompt);
            // Update state with the new response
            const updatedState = {
                ...state,
                messages: [
                    ...state.messages,
                    {
                        role: 'assistant',
                        content: response
                    }
                ]
            };
            return {
                content: response,
                state: updatedState,
            };
        }
        catch (error) {
            console.warn('Observation processing failed:', error);
            const fallbackResponse = `Tool ${action.toolName} executed. Result: ${action.result?.output || 'completed'}`;
            return {
                content: fallbackResponse,
                state: {
                    ...state,
                    messages: [
                        ...state.messages,
                        {
                            role: 'assistant',
                            content: fallbackResponse
                        }
                    ]
                },
            };
        }
    }
    /**
     * Generate reflection step for self-critique
     */
    async generateReflection(state) {
        return {
            content: 'Reflecting on progress and adjusting strategy...',
        };
    }
    /**
     * Check if agent has completed its task
     */
    isComplete(state) {
        // Implementation would check for completion criteria
        return false;
    }
    /**
     * Extract final output from state
     */
    extractFinalOutput(state) {
        const lastMessage = state.messages[state.messages.length - 1];
        return lastMessage?.content || 'Task completed';
    }
    // Getters
    get name() {
        return this.config.name;
    }
    get role() {
        return this.config.role;
    }
    get tools() {
        return this.config.tools;
    }
    get handoffs() {
        return this.config.handoffs;
    }
}
exports.Agent = Agent;
/**
 * Utility function to run agent with simplified interface
 */
async function run(agent, input, context) {
    return agent.run(input, context);
}
//# sourceMappingURL=agent.js.map