import { AgentState, NodeFunction, ConditionalFunction, EdgeTargetMap } from "./types";
/**
 * Agent handoff configuration
 */
interface HandoffConfig {
    targetAgent: string;
    condition?: (state: any) => boolean;
    transferData?: (state: any) => any;
}
/**
 * Parallel execution configuration
 */
interface ParallelConfig {
    nodes: string[];
    mergeStrategy: 'first' | 'all' | 'majority' | 'custom';
    customMerger?: (results: any[]) => any;
    timeout?: number;
}
/**
 * Retry configuration
 */
interface RetryConfig {
    maxAttempts: number;
    backoffMs: number;
    exponential: boolean;
    retryCondition?: (error: Error) => boolean;
}
/**
 * Ultra-high-performance Graph implementation
 *
 * Optimizations applied:
 * - Pre-compiled execution paths (no runtime lookups)
 * - Minimal object allocation in hot paths
 * - Direct function calls instead of map operations
 * - Cached conditional evaluations
 * - Zero-cost abstractions for unused features
 * - Agent handoff support
 * - Parallel execution capabilities
 * - Advanced retry mechanisms
 */
export declare class Graph<TState extends AgentState> {
    private readonly nodes;
    private readonly compiledPaths;
    private readonly handoffs;
    private readonly parallelNodes;
    private readonly retryConfigs;
    private entryPoint;
    private isCompiled;
    /**
     * Add a node to the graph (optimized for performance)
     */
    addNode(id: string, action: NodeFunction<TState>): this;
    /**
     * Add a direct edge (optimized compilation)
     */
    addEdge(from: string, to: string): this;
    /**
     * Add conditional edge (pre-compiled for speed)
     */
    addConditionalEdge(from: string, condition: ConditionalFunction<TState>, targetMap: EdgeTargetMap): this;
    /**
     * Set entry point (fast validation)
     */
    setEntryPoint(nodeId: string): this;
    /**
     * Set finish point (convenience method)
     */
    setFinishPoint(nodeId: string): this;
    /**
     * Add agent handoff configuration
     */
    addHandoff(fromNode: string, config: HandoffConfig): this;
    /**
     * Add parallel execution configuration
     */
    addParallel(nodeId: string, config: ParallelConfig): this;
    /**
     * Add retry configuration for a node
     */
    addRetry(nodeId: string, config: RetryConfig): this;
    /**
     * Compile graph for optimal execution (called automatically)
     */
    private compile;
    /**
     * Ultra-fast graph execution (primary method)
     */
    execute(initialState: TState): Promise<TState>;
    /**
     * Stream execution (for monitoring/debugging)
     */
    stream(initialState: TState): AsyncGenerator<TState>;
    /**
     * Get performance metrics and graph info
     */
    getMetrics(): {
        nodeCount: number;
        pathCount: number;
        isCompiled: boolean;
        entryPoint: string | null;
        nodes: string[];
    };
    /**
     * Execute node with retry logic
     */
    private executeWithRetry;
    /**
     * Execute nodes in parallel
     */
    private executeParallel;
    /**
     * Merge parallel execution results
     */
    private mergeParallelResults;
    /**
     * Delay utility for retries
     */
    private delay;
    /**
     * Fast validation (minimal checks)
     */
    validate(): void;
}
export {};
//# sourceMappingURL=graph.d.ts.map