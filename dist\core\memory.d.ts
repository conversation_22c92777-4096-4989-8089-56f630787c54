import { AgentState } from './types.js';
/**
 * Memory types for different storage strategies
 */
export type MemoryType = 'buffer' | 'window' | 'summary' | 'vector' | 'graph';
/**
 * Memory configuration
 */
export interface MemoryConfig {
    type: MemoryType;
    maxTokens?: number;
    maxMessages?: number;
    windowSize?: number;
    vectorStore?: VectorStore;
    persistenceAdapter?: PersistenceAdapter;
    ttl?: number;
}
/**
 * Vector store interface for semantic memory
 */
export interface VectorStore {
    add(documents: MemoryDocument[]): Promise<void>;
    search(query: string, k?: number, filter?: Record<string, any>): Promise<MemoryDocument[]>;
    delete(ids: string[]): Promise<void>;
}
/**
 * Memory document for vector storage
 */
export interface MemoryDocument {
    id: string;
    content: string;
    metadata: Record<string, any>;
    embedding?: number[];
    timestamp: number;
}
/**
 * Persistence adapter for long-term storage
 */
export interface PersistenceAdapter {
    save(threadId: string, state: AgentState): Promise<void>;
    load(threadId: string): Promise<AgentState | null>;
    delete(threadId: string): Promise<void>;
    list(): Promise<string[]>;
}
/**
 * Memory checkpoint for state snapshots
 */
export interface MemoryCheckpoint {
    threadId: string;
    timestamp: number;
    state: AgentState;
    metadata?: Record<string, any>;
}
/**
 * Ultra-high-performance Memory implementation
 *
 * Features:
 * - Multiple memory strategies (buffer, window, summary, vector, graph)
 * - Conversation persistence with checkpointing
 * - Vector-based semantic retrieval
 * - Time-aware memory with TTL
 * - Performance-optimized operations
 * - Thread isolation for multi-agent scenarios
 */
export declare class Memory<TState extends AgentState = AgentState> {
    private readonly config;
    private readonly cache;
    private readonly messageCache;
    constructor(config?: MemoryConfig);
    /**
     * Load memory state for a thread
     */
    load(state: TState): Promise<TState>;
    /**
     * Save memory state for a thread
     */
    save(state: TState): Promise<void>;
    /**
     * Apply memory strategy to manage conversation length
     */
    private applyMemoryStrategy;
    /**
     * Buffer strategy: Keep all messages up to token limit
     */
    private applyBufferStrategy;
    /**
     * Window strategy: Keep only recent messages
     */
    private applyWindowStrategy;
    /**
     * Summary strategy: Summarize old messages
     */
    private applySummaryStrategy;
    /**
     * Vector strategy: Store in vector database
     */
    private applyVectorStrategy;
    /**
     * Graph strategy: Build knowledge graph
     */
    private applyGraphStrategy;
    /**
     * Save messages to vector store for semantic retrieval
     */
    private saveToVectorStore;
    /**
     * Get thread ID from state
     */
    private getThreadId;
    /**
     * Check if checkpoint is still valid (not expired)
     */
    private isValidCheckpoint;
    /**
     * Merge two states, prioritizing new state
     */
    private mergeStates;
    /**
     * Clear memory for a thread
     */
    clear(threadId: string): Promise<void>;
    /**
     * Get memory statistics
     */
    getStats(): {
        cacheSize: number;
        messageCacheSize: number;
        type: MemoryType;
        config: MemoryConfig;
    };
}
/**
 * In-memory persistence adapter for development
 */
export declare class InMemoryPersistenceAdapter implements PersistenceAdapter {
    private readonly storage;
    save(threadId: string, state: AgentState): Promise<void>;
    load(threadId: string): Promise<AgentState | null>;
    delete(threadId: string): Promise<void>;
    list(): Promise<string[]>;
}
/**
 * Create memory with specific configuration
 */
export declare function createMemory<TState extends AgentState = AgentState>(config: MemoryConfig): Memory<TState>;
//# sourceMappingURL=memory.d.ts.map