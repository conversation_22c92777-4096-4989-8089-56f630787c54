"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InMemoryPersistenceAdapter = exports.Memory = void 0;
exports.createMemory = createMemory;
/**
 * Ultra-high-performance Memory implementation
 *
 * Features:
 * - Multiple memory strategies (buffer, window, summary, vector, graph)
 * - Conversation persistence with checkpointing
 * - Vector-based semantic retrieval
 * - Time-aware memory with TTL
 * - Performance-optimized operations
 * - Thread isolation for multi-agent scenarios
 */
class Memory {
    constructor(config = { type: 'buffer' }) {
        this.cache = new Map();
        this.messageCache = new Map();
        this.config = {
            maxTokens: 4000,
            maxMessages: 50,
            windowSize: 10,
            ttl: 24 * 60 * 60 * 1000, // 24 hours
            ...config,
        };
    }
    /**
     * Load memory state for a thread
     */
    async load(state) {
        const threadId = this.getThreadId(state);
        try {
            // Try cache first for performance
            const cached = this.cache.get(threadId);
            if (cached && this.isValidCheckpoint(cached)) {
                return this.mergeStates(state, cached.state);
            }
            // Load from persistence if available
            if (this.config.persistenceAdapter) {
                const persisted = await this.config.persistenceAdapter.load(threadId);
                if (persisted) {
                    const checkpoint = {
                        threadId,
                        timestamp: Date.now(),
                        state: persisted,
                    };
                    this.cache.set(threadId, checkpoint);
                    return this.mergeStates(state, persisted);
                }
            }
            // Load vector memories if available
            if (this.config.vectorStore && state.messages.length > 0) {
                const recentMessages = state.messages.slice(-3);
                const query = recentMessages.map(m => m.content).join(' ');
                const memories = await this.config.vectorStore.search(query, 5, {
                    threadId,
                });
                if (memories.length > 0) {
                    const memoryContent = memories.map(m => m.content).join('\n');
                    return {
                        ...state,
                        metadata: {
                            ...state.metadata,
                            retrievedMemories: memories,
                            memoryContext: memoryContent,
                        },
                    };
                }
            }
            return state;
        }
        catch (error) {
            console.warn('Failed to load memory:', error);
            return state;
        }
    }
    /**
     * Save memory state for a thread
     */
    async save(state) {
        const threadId = this.getThreadId(state);
        try {
            // Apply memory strategy
            const processedState = await this.applyMemoryStrategy(state);
            // Create checkpoint
            const checkpoint = {
                threadId,
                timestamp: Date.now(),
                state: processedState,
            };
            // Cache for fast access
            this.cache.set(threadId, checkpoint);
            // Persist if adapter available
            if (this.config.persistenceAdapter) {
                await this.config.persistenceAdapter.save(threadId, processedState);
            }
            // Save to vector store for semantic retrieval
            if (this.config.vectorStore && processedState.messages.length > 0) {
                await this.saveToVectorStore(threadId, processedState);
            }
        }
        catch (error) {
            console.warn('Failed to save memory:', error);
        }
    }
    /**
     * Apply memory strategy to manage conversation length
     */
    async applyMemoryStrategy(state) {
        switch (this.config.type) {
            case 'buffer':
                return this.applyBufferStrategy(state);
            case 'window':
                return this.applyWindowStrategy(state);
            case 'summary':
                return this.applySummaryStrategy(state);
            case 'vector':
                return this.applyVectorStrategy(state);
            case 'graph':
                return this.applyGraphStrategy(state);
            default:
                return state;
        }
    }
    /**
     * Buffer strategy: Keep all messages up to token limit
     */
    applyBufferStrategy(state) {
        if (state.messages.length <= this.config.maxMessages) {
            return state;
        }
        // Keep system message and recent messages
        const systemMessages = state.messages.filter(m => m.role === 'system');
        const otherMessages = state.messages.filter(m => m.role !== 'system');
        const recentMessages = otherMessages.slice(-this.config.maxMessages + systemMessages.length);
        return {
            ...state,
            messages: [...systemMessages, ...recentMessages],
        };
    }
    /**
     * Window strategy: Keep only recent messages
     */
    applyWindowStrategy(state) {
        const windowSize = this.config.windowSize || 10;
        const systemMessages = state.messages.filter(m => m.role === 'system');
        const recentMessages = state.messages.slice(-windowSize);
        return {
            ...state,
            messages: [...systemMessages, ...recentMessages],
        };
    }
    /**
     * Summary strategy: Summarize old messages
     */
    async applySummaryStrategy(state) {
        // This would integrate with an LLM to create summaries
        // For now, return buffer strategy
        return this.applyBufferStrategy(state);
    }
    /**
     * Vector strategy: Store in vector database
     */
    async applyVectorStrategy(state) {
        // Keep recent messages, store older ones in vector DB
        const recentMessages = state.messages.slice(-this.config.windowSize);
        return {
            ...state,
            messages: recentMessages,
        };
    }
    /**
     * Graph strategy: Build knowledge graph
     */
    async applyGraphStrategy(state) {
        // This would build entity relationships
        // For now, return buffer strategy
        return this.applyBufferStrategy(state);
    }
    /**
     * Save messages to vector store for semantic retrieval
     */
    async saveToVectorStore(threadId, state) {
        if (!this.config.vectorStore)
            return;
        const documents = state.messages
            .filter(m => m.content && m.content.length > 10)
            .map((message, index) => ({
            id: `${threadId}_${Date.now()}_${index}`,
            content: message.content,
            metadata: {
                threadId,
                role: message.role,
                timestamp: Date.now(),
                agentName: state.metadata?.agentName,
            },
            timestamp: Date.now(),
        }));
        if (documents.length > 0) {
            await this.config.vectorStore.add(documents);
        }
    }
    /**
     * Get thread ID from state
     */
    getThreadId(state) {
        return state.metadata?.threadId ||
            state.metadata?.sessionId ||
            state.metadata?.agentName ||
            'default';
    }
    /**
     * Check if checkpoint is still valid (not expired)
     */
    isValidCheckpoint(checkpoint) {
        const now = Date.now();
        return (now - checkpoint.timestamp) < this.config.ttl;
    }
    /**
     * Merge two states, prioritizing new state
     */
    mergeStates(newState, oldState) {
        return {
            ...oldState,
            ...newState,
            messages: [...(oldState.messages || []), ...(newState.messages || [])],
            metadata: {
                ...oldState.metadata,
                ...newState.metadata,
            },
        };
    }
    /**
     * Clear memory for a thread
     */
    async clear(threadId) {
        this.cache.delete(threadId);
        this.messageCache.delete(threadId);
        if (this.config.persistenceAdapter) {
            await this.config.persistenceAdapter.delete(threadId);
        }
    }
    /**
     * Get memory statistics
     */
    getStats() {
        return {
            cacheSize: this.cache.size,
            messageCacheSize: this.messageCache.size,
            type: this.config.type,
            config: this.config,
        };
    }
}
exports.Memory = Memory;
/**
 * In-memory persistence adapter for development
 */
class InMemoryPersistenceAdapter {
    constructor() {
        this.storage = new Map();
    }
    async save(threadId, state) {
        this.storage.set(threadId, JSON.parse(JSON.stringify(state)));
    }
    async load(threadId) {
        const state = this.storage.get(threadId);
        return state ? JSON.parse(JSON.stringify(state)) : null;
    }
    async delete(threadId) {
        this.storage.delete(threadId);
    }
    async list() {
        return Array.from(this.storage.keys());
    }
}
exports.InMemoryPersistenceAdapter = InMemoryPersistenceAdapter;
/**
 * Create memory with specific configuration
 */
function createMemory(config) {
    return new Memory(config);
}
//# sourceMappingURL=memory.js.map