import { describe, test, expect } from '@jest/globals';

/**
 * Basic Setup Test - Verify Framework Components Load
 * 
 * This test verifies that the AG3NTIC framework components can be imported
 * and basic functionality works before running OpenRouter integration tests.
 */

describe('Basic Framework Setup', () => {
  test('should import core framework components', async () => {
    // Test that we can import the core components
    const { Agent, Tool, Memory, Graph } = await import('../src/core/index.js');
    
    expect(Agent).toBeDefined();
    expect(Tool).toBeDefined();
    expect(Memory).toBeDefined();
    expect(Graph).toBeDefined();
  });

  test('should create basic tool', async () => {
    const { Tool } = await import('../src/core/index.js');
    const { z } = await import('zod');
    
    const testTool = new Tool(
      {
        name: 'test_tool',
        description: 'A test tool',
        parameters: z.object({
          input: z.string(),
        }),
      },
      async ({ input }) => {
        return `Processed: ${input}`;
      }
    );

    expect(testTool.name).toBe('test_tool');
    expect(testTool.description).toBe('A test tool');

    const result = await testTool.execute({ input: 'hello' }, {
      agentName: 'test-agent',
      state: { messages: [], metadata: {} }
    });
    expect(result.success).toBe(true);
    expect(result.output).toBe('Processed: hello');
  });

  test('should create basic memory', async () => {
    const { Memory } = await import('../src/core/index.js');

    const memory = new Memory({
      type: 'buffer',
      maxMessages: 10,
    });

    expect(memory).toBeDefined();

    // Test basic memory functionality
    const testState = {
      messages: [
        { role: 'user' as const, content: 'Hello' },
        { role: 'assistant' as const, content: 'Hi there!' }
      ],
      metadata: {}
    };

    // Test loading state (should return the same state for basic memory)
    const loadedState = await memory.load(testState);
    expect(loadedState.messages).toHaveLength(2);
    expect(loadedState.messages[0].content).toBe('Hello');
  });

  test('should create basic agent', async () => {
    const { Agent, Tool, Memory } = await import('../src/core/index.js');
    const { z } = await import('zod');
    
    const testTool = new Tool(
      {
        name: 'echo',
        description: 'Echo the input',
        parameters: z.object({
          text: z.string(),
        }),
      },
      async ({ text }) => {
        return `Echo: ${text}`;
      }
    );

    const memory = new Memory({
      type: 'buffer',
      maxMessages: 5,
    });

    const agent = new Agent({
      name: 'Test Agent',
      instructions: 'You are a test agent that echoes input.',
      role: 'executor',
      tools: [testTool],
      memory,
      maxIterations: 2,
    });

    expect(agent.name).toBe('Test Agent');
    expect(agent.role).toBe('executor');
    expect(agent.tools).toHaveLength(1);
    expect(agent.tools[0].name).toBe('echo');
  });

  test('should create basic graph', async () => {
    const { Graph } = await import('../src/core/index.js');

    const graph = new Graph()
      .addNode('start', async (state) => {
        return {
          ...state,
          messages: [...state.messages, { role: 'assistant' as const, content: 'Started' }]
        };
      })
      .addNode('end', async (state) => {
        return {
          ...state,
          messages: [...state.messages, { role: 'assistant' as const, content: 'Ended' }]
        };
      })
      .setEntryPoint('start')
      .addEdge('start', 'end')
      .addEdge('end', 'END');

    expect(graph).toBeDefined();

    // Test basic execution using execute method
    const initialState = {
      messages: [{ role: 'user' as const, content: 'Test' }],
      metadata: {}
    };

    const result = await graph.execute(initialState);
    expect(result.messages).toHaveLength(3);
    expect(result.messages[1].content).toBe('Started');
    expect(result.messages[2].content).toBe('Ended');
  });
});
