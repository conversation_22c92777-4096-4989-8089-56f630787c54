import { describe, test, expect, beforeEach } from '@jest/globals';
import { z } from 'zod';
import {
  createEnhancedAgent,
  Tool,
  assertAgentResult,
  assertPerformance,
  globalPerformanceMeasurer,
  testConfig,
  openRouterClient
} from './setup.js';

/**
 * OpenRouter Integration Demo Tests
 * 
 * Demonstrates the AG3NTIC framework working with OpenRouter's Kimi-K2 model
 * These tests showcase real LLM interactions and intelligent agent behavior
 */

describe('OpenRouter Kimi-K2 Integration Demo', () => {
  let smartAgent: any;
  let analysisAgent: any;
  let planningAgent: any;

  beforeEach(() => {
    // Create intelligent agents with OpenRouter integration
    smartAgent = createEnhancedAgent({
      name: 'Kimi-K2 Smart Agent',
      instructions: `You are an intelligent AI agent powered by Kimi-K2. You can:
        - Understand complex requests and provide thoughtful responses
        - Use tools when appropriate to solve problems
        - Engage in natural conversation while being helpful and accurate
        - Analyze information and provide insights
        
        Be conversational, intelligent, and helpful in your responses.`,
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'web_search',
            description: 'Search for information on the web',
            parameters: z.object({
              query: z.string().describe('Search query'),
              limit: z.number().default(5).describe('Number of results'),
            }),
          },
          async ({ query, limit }) => {
            // Simulate web search results
            const results = [
              `Relevant article about ${query}`,
              `Research paper on ${query}`,
              `News update regarding ${query}`,
              `Expert analysis of ${query}`,
              `Community discussion about ${query}`,
              `Technical documentation for ${query}`,
              `Tutorial on ${query}`,
              `Case study involving ${query}`
            ];
            const limitedResults = results.slice(0, limit);
            return `Web search results for "${query}":\n${limitedResults.map((result, i) => `${i + 1}. ${result}`).join('\n')}`;
          }
        ),
        new Tool(
          {
            name: 'calculator',
            description: 'Perform mathematical calculations',
            parameters: z.object({
              expression: z.string().describe('Mathematical expression'),
            }),
          },
          async ({ expression }) => {
            try {
              // Safe evaluation for demo purposes
              const result = eval(expression.replace(/[^0-9+\-*/().]/g, ''));
              return `${expression} = ${result}`;
            } catch {
              return `Invalid expression: ${expression}`;
            }
          }
        )
      ],
      maxIterations: 5,
    });

    analysisAgent = createEnhancedAgent({
      name: 'Kimi-K2 Analysis Agent',
      instructions: `You are a data analysis expert powered by Kimi-K2. You excel at:
        - Analyzing data patterns and trends
        - Providing statistical insights
        - Creating comprehensive reports
        - Making data-driven recommendations
        
        Use the analysis tools when working with data and provide detailed, accurate insights.`,
      role: 'analyst',
      tools: [
        new Tool(
          {
            name: 'data_analyzer',
            description: 'Analyze numerical data',
            parameters: z.object({
              data: z.array(z.number()),
              analysis_type: z.enum(['descriptive', 'trend', 'correlation']),
            }),
          },
          async ({ data, analysis_type }) => {
            const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
            const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
            const stdDev = Math.sqrt(variance);
            
            switch (analysis_type) {
              case 'descriptive':
                return `Descriptive Analysis:\nMean: ${mean.toFixed(2)}\nStd Dev: ${stdDev.toFixed(2)}\nMin: ${Math.min(...data)}\nMax: ${Math.max(...data)}`;
              case 'trend':
                const trend = data[data.length - 1] > data[0] ? 'increasing' : 'decreasing';
                return `Trend Analysis:\nOverall trend: ${trend}\nData points: ${data.length}`;
              case 'correlation':
                return `Correlation Analysis:\nVariability: ${stdDev > mean * 0.3 ? 'High' : 'Low'}\nData spread: ${Math.max(...data) - Math.min(...data)}`;
              default:
                return 'Analysis completed';
            }
          }
        )
      ],
      maxIterations: 4,
    });

    planningAgent = createEnhancedAgent({
      name: 'Kimi-K2 Planning Agent',
      instructions: `You are a strategic planning expert powered by Kimi-K2. You specialize in:
        - Breaking down complex projects into manageable tasks
        - Creating realistic timelines and resource estimates
        - Identifying potential risks and mitigation strategies
        - Developing comprehensive project plans
        
        Use the planning tools to create detailed, actionable plans.`,
      role: 'planner',
      tools: [
        new Tool(
          {
            name: 'project_planner',
            description: 'Create a project plan',
            parameters: z.object({
              project: z.string(),
              duration: z.string(),
              complexity: z.enum(['simple', 'moderate', 'complex']),
            }),
          },
          async ({ project, duration, complexity }) => {
            const phases = {
              simple: ['Planning', 'Execution', 'Review'],
              moderate: ['Analysis', 'Planning', 'Design', 'Implementation', 'Testing', 'Deployment'],
              complex: ['Research', 'Analysis', 'Planning', 'Design', 'Development', 'Testing', 'Integration', 'Deployment', 'Monitoring']
            };
            
            const projectPhases = phases[complexity];
            const timeline = projectPhases.map((phase, i) => `Week ${i + 1}: ${phase}`).join('\n');
            
            return `Project Plan for "${project}" (${duration}):\n\n${timeline}\n\nComplexity: ${complexity}\nEstimated phases: ${projectPhases.length}`;
          }
        )
      ],
      maxIterations: 6,
    });
  });

  test('should demonstrate intelligent conversation with Kimi-K2', async () => {
    globalPerformanceMeasurer.start();

    const result = await smartAgent.run(`
      Hello! I'm testing the AG3NTIC framework with Kimi-K2.
      Can you tell me about your capabilities and how you can help with complex tasks?
      Please be specific about what makes you unique as an AI assistant.
    `);

    const duration = globalPerformanceMeasurer.measure('intelligent_conversation');

    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 4);

    expect(result.finalOutput).toBeDefined();
    expect(result.finalOutput.length).toBeGreaterThan(100);

    console.log('🤖 Kimi-K2 Response:', result.finalOutput);
  });

  test('should perform intelligent tool usage with Kimi-K2', async () => {
    globalPerformanceMeasurer.start();

    const result = await smartAgent.run(`
      I need help with a math problem: What is 15 * 23 + 47 - 12?
      Please use the calculator tool to solve this and explain the result.
    `);

    const duration = globalPerformanceMeasurer.measure('intelligent_tool_usage');

    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 3);

    expect(result.finalOutput).toBeDefined();
    expect(result.finalOutput).toContain('380'); // Expected result: 15*23+47-12 = 345+47-12 = 380

    console.log('🧮 Calculator Tool Result:', result.finalOutput);
  });

  test('should demonstrate advanced data analysis with Kimi-K2', async () => {
    globalPerformanceMeasurer.start();

    const result = await analysisAgent.run(`
      Please analyze this sales data from the last 6 months: [120, 135, 150, 165, 180, 195]

      I need a comprehensive analysis including:
      1. Descriptive statistics
      2. Trend analysis
      3. Insights and recommendations

      Use the data_analyzer tool and provide detailed insights.
    `);

    const duration = globalPerformanceMeasurer.measure('advanced_analysis');

    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 4);

    expect(result.finalOutput).toBeDefined();
    expect(result.finalOutput.toLowerCase()).toContain('trend');
    expect(result.finalOutput.toLowerCase()).toContain('analysis');

    console.log('📊 Analysis Result:', result.finalOutput);
  });

  test('should create comprehensive project plans with Kimi-K2', async () => {
    globalPerformanceMeasurer.start();

    const result = await planningAgent.run(`
      I need to plan a new mobile app development project. The app will be a social media platform
      with real-time messaging, photo sharing, and user profiles.

      Timeline: 6 months
      Complexity: Complex

      Please create a comprehensive project plan using the project_planner tool and provide
      additional strategic insights about potential challenges and success factors.
    `);

    const duration = globalPerformanceMeasurer.measure('comprehensive_planning');

    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 5);

    expect(result.finalOutput).toBeDefined();
    expect(result.finalOutput.toLowerCase()).toContain('project');
    expect(result.finalOutput.toLowerCase()).toContain('development');

    console.log('📋 Project Plan:', result.finalOutput);
  });

  test('should handle complex multi-step reasoning with Kimi-K2', async () => {
    globalPerformanceMeasurer.start();

    const result = await smartAgent.run(`
      Here's a complex scenario: A startup company wants to expand internationally.
      They have $500,000 budget, 20 employees, and their current revenue is $2M annually.

      Please help them think through:
      1. Which markets to consider first
      2. What the expansion timeline should look like
      3. Key risks and how to mitigate them
      4. Resource allocation strategy

      Use web search if you need market information, and calculator for any financial projections.
      Provide a comprehensive strategic recommendation.
    `);

    const duration = globalPerformanceMeasurer.measure('complex_reasoning');

    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 6);

    expect(result.finalOutput).toBeDefined();
    expect(result.finalOutput.length).toBeGreaterThan(300);
    expect(result.finalOutput.toLowerCase()).toContain('expansion');

    console.log('🧠 Strategic Reasoning:', result.finalOutput);
  });

  test('should demonstrate OpenRouter API performance and reliability', async () => {
    const performanceTests = [];

    // Run 5 concurrent requests to test API reliability
    for (let i = 0; i < 5; i++) {
      performanceTests.push(
        smartAgent.run(`Test request ${i + 1}: Please provide a brief summary of artificial intelligence applications in ${['healthcare', 'finance', 'education', 'transportation', 'entertainment'][i]}.`)
      );
    }

    globalPerformanceMeasurer.start();

    const results = await Promise.all(performanceTests);

    const duration = globalPerformanceMeasurer.measure('api_performance_test');

    results.forEach((result, i) => {
      assertAgentResult(result);
      expect(result.finalOutput).toBeDefined();
      expect(result.finalOutput.length).toBeGreaterThan(50);
      console.log(`✅ Request ${i + 1} completed: ${result.finalOutput.substring(0, 100)}...`);
    });

    assertPerformance(duration, testConfig.performance.maxExecutionTime * 8);

    console.log(`🚀 API Performance: ${results.length} requests completed in ${duration.toFixed(2)}ms`);
  });

  test('should validate OpenRouter connection and model capabilities', async () => {
    // Test direct OpenRouter client
    const testResponse = await openRouterClient.generateCompletion([
      {
        role: 'system',
        content: 'You are Kimi-K2, an advanced AI model. Please introduce yourself and describe your key capabilities.'
      },
      {
        role: 'user',
        content: 'Hello! Can you tell me about yourself and what makes you unique?'
      }
    ]);

    expect(testResponse).toBeDefined();
    expect(testResponse.length).toBeGreaterThan(50);
    expect(testResponse.toLowerCase()).toContain('kimi');

    console.log('🔍 Direct API Response:', testResponse);

    // Test evaluation capabilities
    const evaluation = await openRouterClient.generateEvaluation(
      'What is the capital of France?',
      'The capital of France is Paris.',
      ['accuracy', 'completeness', 'clarity']
    );

    expect(evaluation.score).toBeGreaterThan(0.5);
    expect(evaluation.feedback).toBeDefined();
    expect(evaluation.metrics).toBeDefined();

    console.log('📊 Evaluation Result:', evaluation);
  });
});
