import { AgentState, MCPMessage } from './types.js';
import { Agent } from './agent.js';

/**
 * Evaluation metrics
 */
export interface EvaluationMetrics {
  accuracy?: number;
  relevance?: number;
  completeness?: number;
  coherence?: number;
  factuality?: number;
  safety?: number;
  efficiency?: number;
  custom?: Record<string, number>;
}

/**
 * Evaluation result
 */
export interface EvaluationResult {
  score: number;
  metrics: EvaluationMetrics;
  feedback: string;
  suggestions?: string[];
  confidence: number;
  timestamp: number;
}

/**
 * Human feedback interface
 */
export interface HumanFeedback {
  approved: boolean;
  score?: number;
  comments?: string;
  corrections?: string;
  timestamp: number;
  userId?: string;
}

/**
 * Evaluation configuration
 */
export interface EvaluationConfig {
  enableSelfEvaluation: boolean;
  enablePeerReview: boolean;
  enableHumanReview: boolean;
  evaluationAgent?: Agent;
  humanReviewThreshold?: number;
  metrics: (keyof EvaluationMetrics)[];
  customEvaluators?: Map<string, EvaluatorFunction>;
}

/**
 * Evaluator function type
 */
export type EvaluatorFunction = (
  input: string,
  output: string,
  context?: Record<string, any>
) => Promise<EvaluationResult> | EvaluationResult;

/**
 * Scorecard for tracking performance
 */
export interface Scorecard {
  agentName: string;
  totalEvaluations: number;
  averageScore: number;
  metrics: {
    [K in keyof EvaluationMetrics]: {
      average: number;
      count: number;
      trend: 'improving' | 'declining' | 'stable';
    };
  };
  recentEvaluations: EvaluationResult[];
  humanFeedback: HumanFeedback[];
  lastUpdated: number;
}

/**
 * Ultra-high-performance Evaluation system
 * 
 * Features:
 * - Self-evaluation capabilities
 * - Peer review between agents
 * - Human-in-the-loop workflows
 * - Performance scorecards
 * - Custom evaluation metrics
 * - Trend analysis and improvement tracking
 */
export class EvaluationSystem {
  private readonly config: Required<EvaluationConfig>;
  private readonly scorecards = new Map<string, Scorecard>();
  private readonly pendingReviews = new Map<string, {
    input: string;
    output: string;
    context?: Record<string, any>;
    timestamp: number;
  }>();

  constructor(config: EvaluationConfig) {
    this.config = {
      enableSelfEvaluation: true,
      enablePeerReview: false,
      enableHumanReview: false,
      humanReviewThreshold: 0.7,
      metrics: ['accuracy', 'relevance', 'completeness'],
      customEvaluators: new Map(),
      ...config,
    };
  }

  /**
   * Evaluate agent output with multiple evaluation methods
   */
  async evaluate(
    agentName: string,
    input: string,
    output: string,
    context?: Record<string, any>
  ): Promise<EvaluationResult> {
    const evaluations: EvaluationResult[] = [];

    // Self-evaluation
    if (this.config.enableSelfEvaluation) {
      const selfEval = await this.performSelfEvaluation(input, output, context);
      evaluations.push(selfEval);
    }

    // Peer review
    if (this.config.enablePeerReview && this.config.evaluationAgent) {
      const peerEval = await this.performPeerReview(input, output, context);
      evaluations.push(peerEval);
    }

    // Custom evaluators
    for (const [name, evaluator] of this.config.customEvaluators) {
      try {
        const customEval = await evaluator(input, output, context);
        evaluations.push(customEval);
      } catch (error) {
        console.warn(`Custom evaluator ${name} failed:`, error);
      }
    }

    // Combine evaluations
    const combinedResult = this.combineEvaluations(evaluations);

    // Update scorecard
    this.updateScorecard(agentName, combinedResult);

    // Check if human review is needed
    if (this.config.enableHumanReview && 
        combinedResult.score < this.config.humanReviewThreshold) {
      await this.requestHumanReview(agentName, input, output, context);
    }

    return combinedResult;
  }

  /**
   * Perform self-evaluation using the agent's own reasoning
   */
  private async performSelfEvaluation(
    input: string,
    output: string,
    context?: Record<string, any>
  ): Promise<EvaluationResult> {
    // This would use an LLM to evaluate its own output
    // For now, return a basic evaluation
    const metrics: EvaluationMetrics = {};
    
    // Simple heuristics for demonstration
    metrics.completeness = output.length > 50 ? 0.8 : 0.5;
    metrics.relevance = output.toLowerCase().includes(input.toLowerCase().split(' ')[0]) ? 0.9 : 0.6;
    
    const score = Object.values(metrics).reduce((sum, val) => sum + (val || 0), 0) / 
                  Object.keys(metrics).length;

    return {
      score,
      metrics,
      feedback: 'Self-evaluation completed',
      confidence: 0.7,
      timestamp: Date.now(),
    };
  }

  /**
   * Perform peer review using another agent
   */
  private async performPeerReview(
    input: string,
    output: string,
    context?: Record<string, any>
  ): Promise<EvaluationResult> {
    if (!this.config.evaluationAgent) {
      throw new Error('No evaluation agent configured for peer review');
    }

    const evaluationPrompt = `
Please evaluate the following response:

Input: ${input}
Output: ${output}

Rate the response on a scale of 0-1 for:
- Accuracy: How factually correct is the response?
- Relevance: How well does it address the input?
- Completeness: How thorough is the response?
- Coherence: How well-structured and logical is it?

Provide specific feedback and suggestions for improvement.
`;

    try {
      const result = await this.config.evaluationAgent.run(evaluationPrompt);
      
      // Parse the evaluation result (this would be more sophisticated in practice)
      return {
        score: 0.8, // Would parse from agent response
        metrics: {
          accuracy: 0.8,
          relevance: 0.9,
          completeness: 0.7,
          coherence: 0.8,
        },
        feedback: result.finalOutput,
        confidence: 0.9,
        timestamp: Date.now(),
      };
    } catch (error) {
      console.warn('Peer review failed:', error);
      return {
        score: 0.5,
        metrics: {},
        feedback: 'Peer review failed',
        confidence: 0.1,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Request human review for low-scoring outputs
   */
  private async requestHumanReview(
    agentName: string,
    input: string,
    output: string,
    context?: Record<string, any>
  ): Promise<void> {
    const reviewId = `${agentName}_${Date.now()}`;
    
    this.pendingReviews.set(reviewId, {
      input,
      output,
      context,
      timestamp: Date.now(),
    });

    // In a real implementation, this would trigger a notification
    // to human reviewers through a UI, email, or webhook
    console.log(`Human review requested for agent ${agentName}: ${reviewId}`);
  }

  /**
   * Submit human feedback
   */
  async submitHumanFeedback(
    reviewId: string,
    feedback: HumanFeedback
  ): Promise<void> {
    const review = this.pendingReviews.get(reviewId);
    if (!review) {
      throw new Error(`Review ${reviewId} not found`);
    }

    // Process human feedback and update scorecards
    // This would integrate with the evaluation system
    console.log('Human feedback received:', feedback);
    
    this.pendingReviews.delete(reviewId);
  }

  /**
   * Combine multiple evaluation results
   */
  private combineEvaluations(evaluations: EvaluationResult[]): EvaluationResult {
    if (evaluations.length === 0) {
      return {
        score: 0.5,
        metrics: {},
        feedback: 'No evaluations performed',
        confidence: 0.1,
        timestamp: Date.now(),
      };
    }

    // Weighted average based on confidence
    const totalWeight = evaluations.reduce((sum, eval) => sum + eval.confidence, 0);
    const weightedScore = evaluations.reduce(
      (sum, eval) => sum + (eval.score * eval.confidence), 0
    ) / totalWeight;

    // Combine metrics
    const combinedMetrics: EvaluationMetrics = {};
    for (const evaluation of evaluations) {
      for (const [metric, value] of Object.entries(evaluation.metrics)) {
        if (value !== undefined) {
          combinedMetrics[metric as keyof EvaluationMetrics] = 
            (combinedMetrics[metric as keyof EvaluationMetrics] || 0) + value;
        }
      }
    }

    // Average the metrics
    for (const metric in combinedMetrics) {
      combinedMetrics[metric as keyof EvaluationMetrics] = 
        combinedMetrics[metric as keyof EvaluationMetrics]! / evaluations.length;
    }

    return {
      score: weightedScore,
      metrics: combinedMetrics,
      feedback: evaluations.map(e => e.feedback).join('; '),
      suggestions: evaluations.flatMap(e => e.suggestions || []),
      confidence: totalWeight / evaluations.length,
      timestamp: Date.now(),
    };
  }

  /**
   * Update agent scorecard with new evaluation
   */
  private updateScorecard(agentName: string, evaluation: EvaluationResult): void {
    let scorecard = this.scorecards.get(agentName);
    
    if (!scorecard) {
      scorecard = {
        agentName,
        totalEvaluations: 0,
        averageScore: 0,
        metrics: {} as any,
        recentEvaluations: [],
        humanFeedback: [],
        lastUpdated: Date.now(),
      };
      this.scorecards.set(agentName, scorecard);
    }

    // Update totals
    scorecard.totalEvaluations++;
    scorecard.averageScore = (
      (scorecard.averageScore * (scorecard.totalEvaluations - 1)) + evaluation.score
    ) / scorecard.totalEvaluations;

    // Update metrics
    for (const [metric, value] of Object.entries(evaluation.metrics)) {
      if (value !== undefined) {
        if (!scorecard.metrics[metric as keyof EvaluationMetrics]) {
          scorecard.metrics[metric as keyof EvaluationMetrics] = {
            average: 0,
            count: 0,
            trend: 'stable',
          };
        }
        
        const metricData = scorecard.metrics[metric as keyof EvaluationMetrics]!;
        metricData.average = ((metricData.average * metricData.count) + value) / (metricData.count + 1);
        metricData.count++;
      }
    }

    // Keep recent evaluations (last 10)
    scorecard.recentEvaluations.push(evaluation);
    if (scorecard.recentEvaluations.length > 10) {
      scorecard.recentEvaluations.shift();
    }

    scorecard.lastUpdated = Date.now();
  }

  /**
   * Get scorecard for an agent
   */
  getScorecard(agentName: string): Scorecard | undefined {
    return this.scorecards.get(agentName);
  }

  /**
   * Get all scorecards
   */
  getAllScorecards(): Scorecard[] {
    return Array.from(this.scorecards.values());
  }

  /**
   * Get pending human reviews
   */
  getPendingReviews(): Array<{
    id: string;
    input: string;
    output: string;
    context?: Record<string, any>;
    timestamp: number;
  }> {
    return Array.from(this.pendingReviews.entries()).map(([id, review]) => ({
      id,
      ...review,
    }));
  }

  /**
   * Set the evaluation agent for peer reviews
   */
  setEvaluationAgent(agent: Agent): void {
    (this.config as any).evaluationAgent = agent;
  }
}

/**
 * Create evaluation system with configuration
 */
export function createEvaluationSystem(config: EvaluationConfig): EvaluationSystem {
  return new EvaluationSystem(config);
}

/**
 * Global evaluation system instance
 */
export const globalEvaluationSystem = new EvaluationSystem({
  enableSelfEvaluation: true,
  enablePeerReview: false,
  enableHumanReview: false,
  metrics: ['accuracy', 'relevance', 'completeness'],
});
