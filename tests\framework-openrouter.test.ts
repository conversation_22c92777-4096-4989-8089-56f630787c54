import { describe, test, expect } from '@jest/globals';
import { z } from 'zod';

/**
 * Framework + OpenRouter Integration Test
 * 
 * Tests the updated AG3NTIC framework with real OpenRouter integration
 */

// Simple Agent implementation for testing
class SimpleAgent {
  name: string;
  instructions: string;
  tools: any[];
  
  constructor(config: { name: string; instructions: string; tools?: any[] }) {
    this.name = config.name;
    this.instructions = config.instructions;
    this.tools = config.tools || [];
  }

  async run(input: string): Promise<{ finalOutput: string; finalState: any; steps: any[]; metrics: any }> {
    // Direct OpenRouter integration
    const apiKey = '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';
    const model = 'moonshotai/kimi-k2';
    
    const messages = [
      {
        role: 'system' as const,
        content: `You are ${this.name}. ${this.instructions}`
      },
      {
        role: 'user' as const,
        content: input
      }
    ];

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://ag3ntic-framework.dev',
          'X-Title': 'AG3NTIC Framework Tests',
        },
        body: JSON.stringify({
          model,
          messages,
          max_tokens: 2048,
          temperature: 0.7,
          stream: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`);
      }

      const data = await response.json() as any;
      const finalOutput = data.choices[0].message.content;

      return {
        finalOutput,
        finalState: {
          messages: [...messages, { role: 'assistant', content: finalOutput }],
          metadata: { agentName: this.name }
        },
        steps: [
          { step: 'thought', content: 'Processing request with OpenRouter', timestamp: Date.now() },
          { step: 'observation', content: finalOutput, timestamp: Date.now() }
        ],
        metrics: {
          totalSteps: 2,
          toolCalls: 0,
          executionTime: 1000,
        }
      };
    } catch (error) {
      throw new Error(`Agent execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Simple Tool implementation
class SimpleTool {
  name: string;
  description: string;
  schema: any;
  executeFunction: (params: any) => Promise<string>;

  constructor(config: { name: string; description: string; parameters: any }, executeFunction: (params: any) => Promise<string>) {
    this.name = config.name;
    this.description = config.description;
    this.schema = config.parameters;
    this.executeFunction = executeFunction;
  }

  async execute(params: any): Promise<{ success: boolean; output: string }> {
    try {
      const output = await this.executeFunction(params);
      return { success: true, output };
    } catch (error) {
      return { 
        success: false, 
        output: `Tool execution failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }
}

describe('Framework + OpenRouter Integration Tests', () => {
  test('should create and run a simple agent with OpenRouter', async () => {
    console.log('🤖 Testing simple agent with OpenRouter...');
    
    const agent = new SimpleAgent({
      name: 'Test Assistant',
      instructions: 'You are a helpful AI assistant. Provide clear, concise responses.'
    });

    const result = await agent.run('Hello! Please introduce yourself and tell me what you can help with.');
    
    expect(result).toBeDefined();
    expect(result.finalOutput).toBeDefined();
    expect(result.finalOutput.length).toBeGreaterThan(50);
    expect(result.finalState.messages).toHaveLength(3); // system, user, assistant
    expect(result.steps).toHaveLength(2);
    expect(result.metrics.totalSteps).toBe(2);
    
    console.log('✅ Agent Response:', result.finalOutput.substring(0, 100) + '...');
  });

  test('should handle multi-turn conversations', async () => {
    console.log('💬 Testing multi-turn conversation...');
    
    const agent = new SimpleAgent({
      name: 'Conversation Agent',
      instructions: 'You are a conversational AI that remembers context from previous messages.'
    });

    // First turn
    const result1 = await agent.run('My name is Alice and I love programming.');
    expect(result1.finalOutput).toBeDefined();
    expect(result1.finalOutput.toLowerCase()).toContain('alice');
    
    console.log('💬 Turn 1:', result1.finalOutput.substring(0, 80) + '...');

    // Second turn - create new agent with conversation history
    const conversationAgent = new SimpleAgent({
      name: 'Conversation Agent',
      instructions: `You are a conversational AI. Previous conversation:
User: My name is Alice and I love programming.
Assistant: ${result1.finalOutput}

Continue the conversation naturally, remembering what the user told you.`
    });

    const result2 = await conversationAgent.run('What programming languages do you think I should learn?');
    expect(result2.finalOutput).toBeDefined();
    expect(result2.finalOutput.toLowerCase()).toContain('programming');
    
    console.log('💬 Turn 2:', result2.finalOutput.substring(0, 80) + '...');
  });

  test('should work with tools', async () => {
    console.log('🔧 Testing agent with tools...');
    
    const calculatorTool = new SimpleTool(
      {
        name: 'calculator',
        description: 'Perform mathematical calculations',
        parameters: z.object({
          expression: z.string()
        })
      },
      async ({ expression }) => {
        // Safe evaluation for demo
        try {
          const result = eval(expression.replace(/[^0-9+\-*/().]/g, ''));
          return `${expression} = ${result}`;
        } catch {
          return `Invalid expression: ${expression}`;
        }
      }
    );

    const agent = new SimpleAgent({
      name: 'Math Assistant',
      instructions: `You are a math assistant. When users ask for calculations, use the calculator tool.
      Available tools: calculator - performs mathematical calculations.
      
      If the user asks for math, respond with: "I'll calculate that for you: [calculation result]"`
    });

    // Test tool usage
    const toolResult = await calculatorTool.execute({ expression: '25 * 4 + 10' });
    expect(toolResult.success).toBe(true);
    expect(toolResult.output).toContain('110');
    
    console.log('🔧 Tool Result:', toolResult.output);

    // Test agent with math request
    const result = await agent.run('What is 25 * 4 + 10?');
    expect(result.finalOutput).toBeDefined();
    expect(result.finalOutput.toLowerCase()).toContain('calculate');
    
    console.log('🔧 Agent Math Response:', result.finalOutput.substring(0, 100) + '...');
  });

  test('should handle different agent roles', async () => {
    console.log('🎭 Testing different agent roles...');
    
    const plannerAgent = new SimpleAgent({
      name: 'Strategic Planner',
      instructions: 'You are a strategic planning expert. Break down complex tasks into actionable steps and provide detailed plans.'
    });

    const executorAgent = new SimpleAgent({
      name: 'Task Executor',
      instructions: 'You are a task execution specialist. Focus on practical implementation and getting things done efficiently.'
    });

    const analystAgent = new SimpleAgent({
      name: 'Data Analyst',
      instructions: 'You are a data analysis expert. Provide insights, identify patterns, and make data-driven recommendations.'
    });

    // Test planner
    const planResult = await plannerAgent.run('Create a plan for launching a new mobile app.');
    expect(planResult.finalOutput).toBeDefined();
    expect(planResult.finalOutput.toLowerCase()).toContain('plan');
    console.log('📋 Planner:', planResult.finalOutput.substring(0, 80) + '...');

    // Test executor
    const execResult = await executorAgent.run('Execute the first step of app development: market research.');
    expect(execResult.finalOutput).toBeDefined();
    expect(execResult.finalOutput.toLowerCase()).toContain('research');

    // Test analyst
    const analysisResult = await analystAgent.run('Analyze this data: [100, 120, 135, 150, 165] - what trends do you see?');
    expect(analysisResult.finalOutput).toBeDefined();
    expect(analysisResult.finalOutput.toLowerCase()).toContain('trend');
  });

  test('should demonstrate agent coordination', async () => {
    console.log('🤝 Testing agent coordination...');
    
    const coordinatorAgent = new SimpleAgent({
      name: 'Project Coordinator',
      instructions: 'You coordinate between different specialists. Analyze tasks and determine which specialist should handle each part.'
    });

    const task = 'We need to build a data dashboard that shows sales trends and provides strategic recommendations.';
    
    const coordinationResult = await coordinatorAgent.run(`
      Task: ${task}
      
      Available specialists:
      - Data Analyst: Analyzes data and identifies trends
      - Strategic Planner: Creates plans and recommendations
      - Technical Executor: Implements solutions
      
      How should we coordinate this project? Who should do what?
    `);

    expect(coordinationResult.finalOutput).toBeDefined();
    expect(coordinationResult.finalOutput.toLowerCase()).toContain('analyst');
    expect(coordinationResult.finalOutput.toLowerCase()).toContain('planner');
    expect(coordinationResult.finalOutput.toLowerCase()).toContain('executor');
    
    console.log('🤝 Coordination Plan:', coordinationResult.finalOutput.substring(0, 150) + '...');
  });

  test('should handle error scenarios gracefully', async () => {
    console.log('⚠️ Testing error handling...');
    
    const agent = new SimpleAgent({
      name: 'Test Agent',
      instructions: 'You are a test agent for error handling scenarios.'
    });

    // Test with very long input
    const longInput = 'A'.repeat(10000);
    
    try {
      const result = await agent.run(`Process this long input: ${longInput}`);
      expect(result.finalOutput).toBeDefined();
      console.log('✅ Long input handled successfully');
    } catch (error) {
      console.log('⚠️ Long input caused error (expected):', error instanceof Error ? error.message : 'Unknown error');
      expect(error).toBeDefined();
    }

    // Test with normal input to ensure agent still works
    const normalResult = await agent.run('Hello, are you working correctly?');
    expect(normalResult.finalOutput).toBeDefined();
    expect(normalResult.finalOutput.length).toBeGreaterThan(10);
    
    console.log('✅ Normal operation after error test:', normalResult.finalOutput.substring(0, 50) + '...');
  });
});
