"use strict";
// src/core/graph.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.Graph = void 0;
const types_1 = require("./types");
/**
 * Ultra-high-performance Graph implementation
 *
 * Optimizations applied:
 * - Pre-compiled execution paths (no runtime lookups)
 * - Minimal object allocation in hot paths
 * - Direct function calls instead of map operations
 * - Cached conditional evaluations
 * - Zero-cost abstractions for unused features
 * - Agent handoff support
 * - Parallel execution capabilities
 * - Advanced retry mechanisms
 */
class Graph {
    constructor() {
        this.nodes = new Map();
        this.compiledPaths = new Map();
        this.handoffs = new Map();
        this.parallelNodes = new Map();
        this.retryConfigs = new Map();
        this.entryPoint = null;
        this.isCompiled = false;
    }
    /**
     * Add a node to the graph (optimized for performance)
     */
    addNode(id, action) {
        this.nodes.set(id, action);
        this.isCompiled = false;
        // Auto-set entry point for convenience
        if (!this.entryPoint) {
            this.entryPoint = id;
        }
        return this;
    }
    /**
     * Add a direct edge (optimized compilation)
     */
    addEdge(from, to) {
        const path = this.compiledPaths.get(from) || {
            node: this.nodes.get(from),
            nextNode: null,
            isConditional: false
        };
        this.compiledPaths.set(from, {
            ...path,
            nextNode: to === types_1.END ? null : to
        });
        this.isCompiled = false;
        return this;
    }
    /**
     * Add conditional edge (pre-compiled for speed)
     */
    addConditionalEdge(from, condition, targetMap) {
        // Pre-compile conditional mapping for O(1) lookup
        const optimizedMap = {};
        for (const [key, value] of Object.entries(targetMap)) {
            optimizedMap[key] = value === types_1.END ? null : value;
        }
        this.compiledPaths.set(from, {
            node: this.nodes.get(from),
            nextNode: null,
            isConditional: true,
            conditionalFn: condition,
            conditionalMap: optimizedMap
        });
        this.isCompiled = false;
        return this;
    }
    /**
     * Set entry point (fast validation)
     */
    setEntryPoint(nodeId) {
        this.entryPoint = nodeId;
        return this;
    }
    /**
     * Set finish point (convenience method)
     */
    setFinishPoint(nodeId) {
        return this.addEdge(nodeId, types_1.END);
    }
    /**
     * Add agent handoff configuration
     */
    addHandoff(fromNode, config) {
        this.handoffs.set(fromNode, config);
        return this;
    }
    /**
     * Add parallel execution configuration
     */
    addParallel(nodeId, config) {
        this.parallelNodes.set(nodeId, config);
        return this;
    }
    /**
     * Add retry configuration for a node
     */
    addRetry(nodeId, config) {
        this.retryConfigs.set(nodeId, config);
        return this;
    }
    /**
     * Compile graph for optimal execution (called automatically)
     */
    compile() {
        if (this.isCompiled)
            return;
        // Ensure all nodes have compiled paths
        for (const [name, nodeFunction] of this.nodes) {
            if (!this.compiledPaths.has(name)) {
                this.compiledPaths.set(name, {
                    node: nodeFunction,
                    nextNode: null,
                    isConditional: false
                });
            }
            else {
                // Update node reference
                const path = this.compiledPaths.get(name);
                path.node = nodeFunction;
            }
        }
        this.isCompiled = true;
    }
    /**
     * Ultra-fast graph execution (primary method)
     */
    async execute(initialState) {
        if (!this.entryPoint) {
            throw new Error('No entry point set');
        }
        this.compile();
        let currentNodeKey = this.entryPoint;
        let state = initialState; // Direct reference for performance
        // Hot path: optimized execution loop
        while (currentNodeKey) {
            const path = this.compiledPaths.get(currentNodeKey);
            if (!path) {
                throw new Error(`Node '${currentNodeKey}' not found`);
            }
            // Check for parallel execution
            if (this.parallelNodes.has(currentNodeKey)) {
                const parallelConfig = this.parallelNodes.get(currentNodeKey);
                const results = await this.executeParallel(parallelConfig, state);
                state = this.mergeParallelResults(results, parallelConfig);
            }
            else {
                // Execute node function with retry logic
                const result = await this.executeWithRetry(currentNodeKey, path.node, state);
                // Efficient state update - only spread if result has properties
                if (result && typeof result === 'object' && Object.keys(result).length > 0) {
                    state = { ...state, ...result };
                }
            }
            // Check for agent handoff
            if (this.handoffs.has(currentNodeKey)) {
                const handoffConfig = this.handoffs.get(currentNodeKey);
                if (!handoffConfig.condition || handoffConfig.condition(state)) {
                    // Prepare data for handoff
                    const transferData = handoffConfig.transferData ?
                        handoffConfig.transferData(state) : state;
                    // Add handoff metadata
                    state = {
                        ...state,
                        metadata: {
                            ...state.metadata,
                            handoffTarget: handoffConfig.targetAgent,
                            handoffData: transferData,
                        },
                    };
                    break; // Exit current graph execution
                }
            }
            // Fast next node resolution
            if (path.isConditional && path.conditionalFn && path.conditionalMap) {
                const conditionResult = path.conditionalFn(state);
                currentNodeKey = path.conditionalMap[conditionResult] || null;
            }
            else {
                currentNodeKey = path.nextNode;
            }
        }
        return state;
    }
    /**
     * Stream execution (for monitoring/debugging)
     */
    async *stream(initialState) {
        if (!this.entryPoint) {
            throw new Error('No entry point set');
        }
        this.compile();
        let currentNodeKey = this.entryPoint;
        let state = initialState;
        while (currentNodeKey) {
            const path = this.compiledPaths.get(currentNodeKey);
            if (!path) {
                throw new Error(`Node '${currentNodeKey}' not found`);
            }
            const result = await path.node(state);
            if (result && typeof result === 'object' && Object.keys(result).length > 0) {
                state = { ...state, ...result };
            }
            yield state;
            // Get next node
            if (path.isConditional && path.conditionalFn && path.conditionalMap) {
                const conditionResult = path.conditionalFn(state);
                currentNodeKey = path.conditionalMap[conditionResult] || null;
            }
            else {
                currentNodeKey = path.nextNode;
            }
        }
    }
    /**
     * Get performance metrics and graph info
     */
    getMetrics() {
        return {
            nodeCount: this.nodes.size,
            pathCount: this.compiledPaths.size,
            isCompiled: this.isCompiled,
            entryPoint: this.entryPoint,
            nodes: Array.from(this.nodes.keys())
        };
    }
    /**
     * Execute node with retry logic
     */
    async executeWithRetry(nodeKey, nodeFunction, state) {
        const retryConfig = this.retryConfigs.get(nodeKey);
        if (!retryConfig) {
            return nodeFunction(state);
        }
        let lastError = null;
        for (let attempt = 0; attempt < retryConfig.maxAttempts; attempt++) {
            try {
                return await nodeFunction(state);
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                // Check if we should retry this error
                if (retryConfig.retryCondition && !retryConfig.retryCondition(lastError)) {
                    throw lastError;
                }
                // Don't wait after the last attempt
                if (attempt < retryConfig.maxAttempts - 1) {
                    const delay = retryConfig.exponential
                        ? retryConfig.backoffMs * Math.pow(2, attempt)
                        : retryConfig.backoffMs;
                    await this.delay(delay);
                }
            }
        }
        throw lastError;
    }
    /**
     * Execute nodes in parallel
     */
    async executeParallel(config, state) {
        const promises = config.nodes.map(nodeKey => {
            const nodeFunction = this.nodes.get(nodeKey);
            if (!nodeFunction) {
                throw new Error(`Parallel node '${nodeKey}' not found`);
            }
            return this.executeWithRetry(nodeKey, nodeFunction, state);
        });
        if (config.timeout) {
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Parallel execution timeout')), config.timeout);
            });
            return Promise.race([Promise.all(promises), timeoutPromise]);
        }
        return Promise.all(promises);
    }
    /**
     * Merge parallel execution results
     */
    mergeParallelResults(results, config) {
        switch (config.mergeStrategy) {
            case 'first':
                return results.find(r => r !== undefined);
            case 'all':
                return results.reduce((acc, result) => ({ ...acc, ...result }), {});
            case 'majority':
                // Simple majority logic - could be enhanced
                return results[Math.floor(results.length / 2)];
            case 'custom':
                if (config.customMerger) {
                    return config.customMerger(results);
                }
                throw new Error('Custom merger not provided');
            default:
                return results[0];
        }
    }
    /**
     * Delay utility for retries
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Fast validation (minimal checks)
     */
    validate() {
        if (!this.entryPoint || !this.nodes.has(this.entryPoint)) {
            throw new Error('Invalid entry point');
        }
    }
}
exports.Graph = Graph;
//# sourceMappingURL=graph.js.map